<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;

class DynamicStyleController extends Controller
{
    public function generateCSS()
    {
        // Get all settings
        $settings = Setting::pluck('value', 'key')->toArray();
        
        // Default values
        $defaults = [
            'primary_color' => '#667eea',
            'secondary_color' => '#764ba2',
            'accent_color' => '#f093fb',
            'text_color' => '#333333',
            'background_color' => '#ffffff',
            'navbar_background' => 'rgba(255, 255, 255, 0.95)',
        ];
        
        // Merge with defaults
        foreach ($defaults as $key => $default) {
            if (!isset($settings[$key]) || empty($settings[$key])) {
                $settings[$key] = $default;
            }
        }
        
        // Generate CSS content
        $css = ":root {\n";
        $css .= "    --primary-color: {$settings['primary_color']};\n";
        $css .= "    --secondary-color: {$settings['secondary_color']};\n";
        $css .= "    --accent-color: {$settings['accent_color']};\n";
        $css .= "    --text-color: {$settings['text_color']};\n";
        $css .= "    --background-color: {$settings['background_color']};\n";
        $css .= "    --navbar-background: {$settings['navbar_background']};\n";
        
        // Generate derived colors
        $css .= "    --primary-light: color-mix(in srgb, var(--primary-color) 20%, white);\n";
        $css .= "    --primary-dark: color-mix(in srgb, var(--primary-color) 80%, black);\n";
        $css .= "    --secondary-light: color-mix(in srgb, var(--secondary-color) 20%, white);\n";
        $css .= "    --secondary-dark: color-mix(in srgb, var(--secondary-color) 80%, black);\n";
        $css .= "    --accent-light: color-mix(in srgb, var(--accent-color) 20%, white);\n";
        $css .= "    --accent-dark: color-mix(in srgb, var(--accent-color) 80%, black);\n";
        $css .= "}\n";
        
        return response($css)
            ->header('Content-Type', 'text/css')
            ->header('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    }
    
    public function previewColors(Request $request)
    {
        $colors = $request->only([
            'primary_color',
            'secondary_color', 
            'accent_color',
            'text_color',
            'background_color',
            'navbar_background'
        ]);
        
        // Generate preview CSS
        $css = ":root {\n";
        foreach ($colors as $key => $value) {
            if (!empty($value)) {
                $cssVar = str_replace('_', '-', $key);
                $css .= "    --{$cssVar}: {$value};\n";
            }
        }
        $css .= "}\n";
        
        return response($css)->header('Content-Type', 'text/css');
    }
}
