<?php

namespace Database\Factories;

use App\Models\Service;
use Illuminate\Database\Eloquent\Factories\Factory;

class ServiceFactory extends Factory
{
    protected $model = Service::class;

    public function definition()
    {
        return [
            'title' => $this->faker->randomElement([
                'Web Development',
                'Mobile App Development',
                'UI/UX Design',
                'E-commerce Solutions',
                'API Development',
                'Database Design'
            ]),
            'description' => $this->faker->paragraph(),
            'features' => implode("\n", $this->faker->sentences(4)),
            'icon' => null,
            'is_active' => $this->faker->boolean(80),
            'order' => $this->faker->numberBetween(1, 100),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }
}
