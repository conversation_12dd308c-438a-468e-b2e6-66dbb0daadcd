// Portfolio Animations and Interactive Elements

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all animations
    initGalaxyBackground();
    initScrollAnimations();
    initTestimonialCarousel();
    initCounterAnimations();
    initMobileMenu();
    initSmoothScrolling();
    initParallaxEffects();
    initImageHoverEffects();
});

// Galaxy Background Animation
function initGalaxyBackground() {
    const hero = document.querySelector('.hero');
    if (!hero) return;
    
    const galaxyBg = document.createElement('div');
    galaxyBg.className = 'galaxy-background';
    hero.appendChild(galaxyBg);
    
    // Create stars
    for (let i = 0; i < 100; i++) {
        createStar(galaxyBg);
    }
    
    // Create shooting stars
    setInterval(() => {
        if (Math.random() < 0.3) {
            createShootingStar(galaxyBg);
        }
    }, 3000);
    
    // Create floating particles
    for (let i = 0; i < 20; i++) {
        createGalaxyParticle(galaxyBg);
    }
}

function createStar(container) {
    const star = document.createElement('div');
    star.className = `star ${['small', 'medium', 'large'][Math.floor(Math.random() * 3)]}`;
    star.style.left = Math.random() * 100 + '%';
    star.style.top = Math.random() * 100 + '%';
    star.style.animationDelay = Math.random() * 3 + 's';
    container.appendChild(star);
}

function createShootingStar(container) {
    const shootingStar = document.createElement('div');
    shootingStar.className = 'shooting-star';
    shootingStar.style.top = Math.random() * 50 + '%';
    shootingStar.style.left = '-100px';
    container.appendChild(shootingStar);
    
    setTimeout(() => {
        if (shootingStar.parentNode) {
            shootingStar.parentNode.removeChild(shootingStar);
        }
    }, 4000);
}

function createGalaxyParticle(container) {
    const particle = document.createElement('div');
    particle.className = 'galaxy-particle';
    particle.style.width = Math.random() * 4 + 2 + 'px';
    particle.style.height = particle.style.width;
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    particle.style.animationDelay = Math.random() * 6 + 's';
    container.appendChild(particle);
}

// Scroll Animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                
                // Animate grid items with stagger
                const gridItems = entry.target.querySelectorAll('.grid > *');
                gridItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('grid-item');
                    }, index * 100);
                });
            }
        });
    }, observerOptions);
    
    // Observe sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('scroll-reveal');
        observer.observe(section);
    });
    
    // Observe individual elements
    document.querySelectorAll('.card, .stat-card').forEach(element => {
        element.classList.add('scroll-reveal');
        observer.observe(element);
    });
}

// Testimonial Carousel
function initTestimonialCarousel() {
    const carousel = document.querySelector('.testimonials-carousel');
    if (!carousel) return;
    
    const slides = carousel.querySelectorAll('.testimonial-slide');
    const dotsContainer = document.createElement('div');
    dotsContainer.className = 'carousel-nav';
    
    let currentSlide = 0;
    let autoplayInterval;
    
    // Create navigation dots
    slides.forEach((_, index) => {
        const dot = document.createElement('button');
        dot.className = 'carousel-dot';
        dot.addEventListener('click', () => goToSlide(index));
        dotsContainer.appendChild(dot);
    });
    
    carousel.appendChild(dotsContainer);
    
    // Create arrow navigation
    const prevArrow = document.createElement('button');
    prevArrow.className = 'carousel-arrow prev';
    prevArrow.innerHTML = '‹';
    prevArrow.addEventListener('click', () => goToSlide(currentSlide - 1));
    
    const nextArrow = document.createElement('button');
    nextArrow.className = 'carousel-arrow next';
    nextArrow.innerHTML = '›';
    nextArrow.addEventListener('click', () => goToSlide(currentSlide + 1));
    
    carousel.appendChild(prevArrow);
    carousel.appendChild(nextArrow);
    
    function goToSlide(index) {
        if (index < 0) index = slides.length - 1;
        if (index >= slides.length) index = 0;
        
        slides[currentSlide].classList.remove('active');
        dotsContainer.children[currentSlide].classList.remove('active');
        
        currentSlide = index;
        
        slides[currentSlide].classList.add('active');
        dotsContainer.children[currentSlide].classList.add('active');
        
        resetAutoplay();
    }
    
    function startAutoplay() {
        autoplayInterval = setInterval(() => {
            goToSlide(currentSlide + 1);
        }, 5000);
    }
    
    function resetAutoplay() {
        clearInterval(autoplayInterval);
        startAutoplay();
    }
    
    // Initialize
    if (slides.length > 0) {
        slides[0].classList.add('active');
        dotsContainer.children[0].classList.add('active');
        startAutoplay();
    }
    
    // Pause on hover
    carousel.addEventListener('mouseenter', () => clearInterval(autoplayInterval));
    carousel.addEventListener('mouseleave', startAutoplay);
}

// Counter Animations
function initCounterAnimations() {
    const counters = document.querySelectorAll('.stat-number');
    
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

function animateCounter(element) {
    const target = parseInt(element.textContent);
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;
    
    element.classList.add('animate');
    
    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 16);
}

// Mobile Menu
function initMobileMenu() {
    const toggle = document.querySelector('.mobile-menu-toggle');
    const menu = document.querySelector('.mobile-menu');
    
    if (!toggle || !menu) return;
    
    toggle.addEventListener('click', () => {
        menu.classList.toggle('active');
        toggle.classList.toggle('active');
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!toggle.contains(e.target) && !menu.contains(e.target)) {
            menu.classList.remove('active');
            toggle.classList.remove('active');
        }
    });
    
    // Close menu when clicking on links
    menu.querySelectorAll('a').forEach(link => {
        link.addEventListener('click', () => {
            menu.classList.remove('active');
            toggle.classList.remove('active');
        });
    });
}

// Smooth Scrolling
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Parallax Effects
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.parallax');
    
    if (parallaxElements.length === 0) return;
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        
        parallaxElements.forEach(element => {
            const rate = scrolled * -0.5;
            element.style.transform = `translateY(${rate}px)`;
        });
    }
    
    // Throttle scroll events
    let ticking = false;
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }
    
    window.addEventListener('scroll', () => {
        requestTick();
        ticking = false;
    });
}

// Image Hover Effects
function initImageHoverEffects() {
    document.querySelectorAll('.project-image, .blog-image').forEach(image => {
        image.classList.add('image-hover');
    });
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Loading states
function showLoading(element) {
    element.classList.add('loading');
    const spinner = document.createElement('span');
    spinner.className = 'loading-spinner';
    element.prepend(spinner);
}

function hideLoading(element) {
    element.classList.remove('loading');
    const spinner = element.querySelector('.loading-spinner');
    if (spinner) {
        spinner.remove();
    }
}

// Progress bar animation
function animateProgressBar(element, percentage) {
    const fill = element.querySelector('.progress-fill');
    if (fill) {
        fill.style.transform = `translateX(-${100 - percentage}%)`;
        fill.classList.add('animate');
    }
}

// Text reveal animation
function initTextReveal() {
    document.querySelectorAll('.text-reveal').forEach(element => {
        const text = element.textContent;
        element.innerHTML = text.split('').map(char => 
            `<span>${char === ' ' ? '&nbsp;' : char}</span>`
        ).join('');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(element);
    });
}

// Export functions for external use
window.PortfolioAnimations = {
    showLoading,
    hideLoading,
    animateProgressBar,
    initTextReveal
};
