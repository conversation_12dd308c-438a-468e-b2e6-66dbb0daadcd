<?php

namespace App\Console\Commands;

use App\Services\CacheService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class OptimizePerformance extends Command
{
    protected $signature = 'portfolio:optimize';
    protected $description = 'Optimize the portfolio application for production';

    public function handle()
    {
        $this->info('Starting portfolio optimization...');
        
        // Clear all caches
        $this->info('Clearing caches...');
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');
        
        // Optimize for production
        $this->info('Optimizing for production...');
        Artisan::call('config:cache');
        Artisan::call('route:cache');
        Artisan::call('view:cache');
        
        // Warm up application caches
        $this->info('Warming up application caches...');
        CacheService::warmUp();
        
        // Generate optimized autoloader
        $this->info('Optimizing autoloader...');
        exec('composer dump-autoload --optimize');
        
        $this->info('Portfolio optimization completed successfully!');
        
        return 0;
    }
}
