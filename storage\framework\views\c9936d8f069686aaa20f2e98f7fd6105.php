<?php $__env->startSection('title', 'Services - <PERSON>'); ?>
<?php $__env->startSection('description', 'Professional web development, design, and creative services offered by <PERSON>.'); ?>

<?php $__env->startSection('content'); ?>
<div class="section" style="padding-top: 6rem;">
    <div class="container">
        <!-- <PERSON>er -->
        <div class="text-center mb-2xl">
            <h1 class="text-5xl font-bold mb-lg">My Services</h1>
            <p class="text-xl text-muted max-w-2xl mx-auto">
                I offer comprehensive digital solutions to help bring your ideas to life. 
                From concept to completion, I'm here to help you succeed.
            </p>
        </div>
        
        <!-- Services Grid -->
        <div class="grid grid-auto-fit gap-xl">
            <?php $__empty_1 = true; $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopD<PERSON> as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="card hover-lift text-center">
                    <div class="service-icon">
                        <?php if($service->icon): ?>
                            <img src="<?php echo e(asset('images/services/' . $service->icon)); ?>" 
                                 alt="<?php echo e($service->title); ?>" 
                                 class="w-16 h-16 mx-auto mb-lg">
                        <?php else: ?>
                            <div class="w-16 h-16 mx-auto mb-lg bg-gradient-primary rounded-full flex items-center justify-center text-white text-2xl">
                                ⚡
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <h3 class="card-title text-xl"><?php echo e($service->title); ?></h3>
                    <p class="card-description mb-lg"><?php echo e($service->description); ?></p>
                    
                    <?php if($service->features): ?>
                        <ul class="text-left mb-lg space-y-2">
                            <?php $__currentLoopData = explode("\n", $service->features); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(trim($feature)): ?>
                                    <li class="flex items-start gap-2">
                                        <span class="text-primary mt-1">✓</span>
                                        <span class="text-sm"><?php echo e(trim($feature)); ?></span>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    <?php endif; ?>
                    
                    <a href="<?php echo e(url('/contact')); ?>" class="btn btn-primary w-full">
                        Get Started
                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-3xl">
                    <div class="text-6xl mb-lg">🛠️</div>
                    <h3 class="text-2xl font-semibold mb-md">Services Coming Soon</h3>
                    <p class="text-muted">Service offerings will be displayed here once they are added.</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Process Section -->
        <div class="mt-3xl">
            <h2 class="text-3xl font-bold text-center mb-2xl">My Process</h2>
            <div class="grid md:grid-cols-4 gap-xl">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-lg bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        1
                    </div>
                    <h3 class="font-semibold mb-md">Discovery</h3>
                    <p class="text-sm text-muted">Understanding your needs, goals, and vision for the project.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-lg bg-secondary rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        2
                    </div>
                    <h3 class="font-semibold mb-md">Planning</h3>
                    <p class="text-sm text-muted">Creating a detailed roadmap and strategy for your project.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-lg bg-accent rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        3
                    </div>
                    <h3 class="font-semibold mb-md">Development</h3>
                    <p class="text-sm text-muted">Building your solution with clean code and best practices.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-lg bg-gradient-primary rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        4
                    </div>
                    <h3 class="font-semibold mb-md">Delivery</h3>
                    <p class="text-sm text-muted">Testing, optimization, and launching your finished project.</p>
                </div>
            </div>
        </div>
        
        <!-- Technologies Section -->
        <div class="mt-3xl">
            <h2 class="text-3xl font-bold text-center mb-2xl">Technologies I Use</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-lg">
                <?php
                $technologies = [
                    ['name' => 'Laravel', 'icon' => '🔥'],
                    ['name' => 'Vue.js', 'icon' => '💚'],
                    ['name' => 'React', 'icon' => '⚛️'],
                    ['name' => 'PHP', 'icon' => '🐘'],
                    ['name' => 'JavaScript', 'icon' => '⚡'],
                    ['name' => 'MySQL', 'icon' => '🗄️'],
                    ['name' => 'HTML5', 'icon' => '🌐'],
                    ['name' => 'CSS3', 'icon' => '🎨'],
                    ['name' => 'Figma', 'icon' => '🎯'],
                    ['name' => 'Photoshop', 'icon' => '🖼️'],
                    ['name' => 'Blender', 'icon' => '🎭'],
                    ['name' => 'Git', 'icon' => '📝']
                ];
                ?>
                
                <?php $__currentLoopData = $technologies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tech): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="text-center p-lg border rounded-lg hover:shadow-md transition">
                        <div class="text-3xl mb-sm"><?php echo e($tech['icon']); ?></div>
                        <div class="text-sm font-medium"><?php echo e($tech['name']); ?></div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        
        <!-- Call to Action -->
        <div class="text-center mt-3xl">
            <div class="bg-gradient-primary rounded-2xl p-3xl text-white">
                <h2 class="text-3xl font-bold mb-lg">Ready to Start Your Project?</h2>
                <p class="text-xl mb-xl opacity-90">
                    Let's discuss how I can help bring your vision to life.
                </p>
                <div class="flex flex-col sm:flex-row gap-md justify-center">
                    <a href="<?php echo e(url('/contact')); ?>" class="btn btn-secondary">
                        Get a Quote
                    </a>
                    <a href="<?php echo e(url('/projects')); ?>" class="btn" style="background: rgba(255,255,255,0.2); color: white;">
                        View My Work
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.w-16 {
    width: 4rem;
}

.h-16 {
    height: 4rem;
}

.w-full {
    width: 100%;
}

.max-w-2xl {
    max-width: 42rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.col-span-full {
    grid-column: 1 / -1;
}

@media (min-width: 768px) {
    .md\:grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-6 {
        grid-template-columns: repeat(6, 1fr);
    }
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\pages\services.blade.php ENDPATH**/ ?>