<?php $__env->startSection('title', 'Projects <PERSON> <PERSON>'); ?>
<?php $__env->startSection('description', 'Explore my portfolio of web development projects, applications, and creative works.'); ?>

<?php $__env->startSection('content'); ?>
<div class="section" style="padding-top: 6rem;">
    <div class="container">
        <!-- Page Header -->
        <div class="text-center mb-2xl">
            <h1 class="text-5xl font-bold mb-lg">My Projects</h1>
            <p class="text-xl text-muted max-w-2xl mx-auto">
                A collection of my recent work in web development, design, and creative projects. 
                Each project represents a unique challenge and solution.
            </p>
        </div>
        
        <!-- Projects Grid -->
        <div class="grid grid-auto-fit gap-xl">
            <?php $__empty_1 = true; $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="card hover-lift">
                    <div class="project-image">
                        <?php if($project->image): ?>
                            <img src="<?php echo e(asset('images/projects/' . $project->image)); ?>" 
                                 alt="<?php echo e($project->title); ?>" 
                                 loading="lazy">
                        <?php else: ?>
                            <div class="bg-gray-100 flex items-center justify-center h-48">
                                <span class="text-4xl">🚀</span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="p-xl">
                        <h3 class="card-title"><?php echo e($project->title); ?></h3>
                        <p class="card-description mb-lg"><?php echo e($project->description); ?></p>
                        
                        <?php if($project->technologies): ?>
                            <div class="flex flex-wrap gap-xs mb-lg">
                                <?php $__currentLoopData = explode(',', $project->technologies); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tech): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="px-sm py-xs bg-gray-100 rounded-full text-xs">
                                        <?php echo e(trim($tech)); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="flex gap-md">
                            <a href="<?php echo e(route('project.detail', $project->id)); ?>" 
                               class="btn btn-primary flex-1">
                                View Details
                            </a>
                            <?php if($project->url): ?>
                                <a href="<?php echo e($project->url); ?>" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   class="btn btn-secondary">
                                    Live Demo
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-3xl">
                    <div class="text-6xl mb-lg">🚧</div>
                    <h3 class="text-2xl font-semibold mb-md">No Projects Yet</h3>
                    <p class="text-muted">Projects will be displayed here once they are added.</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <?php if($projects->hasPages()): ?>
            <div class="mt-3xl">
                <?php echo e($projects->links()); ?>

            </div>
        <?php endif; ?>
        
        <!-- Call to Action -->
        <div class="text-center mt-3xl">
            <div class="bg-gradient-primary rounded-2xl p-3xl text-white">
                <h2 class="text-3xl font-bold mb-lg">Have a Project in Mind?</h2>
                <p class="text-xl mb-xl opacity-90">
                    Let's work together to bring your ideas to life.
                </p>
                <a href="<?php echo e(url('/contact')); ?>" class="btn btn-secondary">
                    Get In Touch
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .active span {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.max-w-2xl {
    max-width: 42rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.col-span-full {
    grid-column: 1 / -1;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\pages\projects.blade.php ENDPATH**/ ?>