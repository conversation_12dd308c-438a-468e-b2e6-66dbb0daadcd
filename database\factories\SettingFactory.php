<?php

namespace Database\Factories;

use App\Models\SiteSetting;
use Illuminate\Database\Eloquent\Factories\Factory;

class SettingFactory extends Factory
{
    protected $model = SiteSetting::class;

    public function definition()
    {
        return [
            'key' => $this->faker->unique()->randomElement([
                'site_name',
                'primary_color',
                'secondary_color',
                'contact_email',
                'phone_number',
                'address',
                'social_facebook',
                'social_twitter',
                'social_linkedin',
                'social_github'
            ]),
            'value' => $this->faker->sentence(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function siteSettings()
    {
        return $this->state(function (array $attributes) {
            return [
                'key' => 'site_name',
                'value' => 'Aziz Khan Portfolio',
            ];
        });
    }

    public function colorSettings()
    {
        return $this->state(function (array $attributes) {
            return [
                'key' => 'primary_color',
                'value' => '#667eea',
            ];
        });
    }
}
