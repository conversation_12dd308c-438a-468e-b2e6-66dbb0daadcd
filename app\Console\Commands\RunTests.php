<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class RunTests extends Command
{
    protected $signature = 'portfolio:test {--coverage : Generate code coverage report}';
    protected $description = 'Run the portfolio application test suite';

    public function handle()
    {
        $this->info('Running Portfolio Test Suite...');
        
        // Run feature tests
        $this->info('Running Feature Tests...');
        $featureResult = $this->runTests('Feature');
        
        // Run unit tests
        $this->info('Running Unit Tests...');
        $unitResult = $this->runTests('Unit');
        
        // Generate coverage report if requested
        if ($this->option('coverage')) {
            $this->info('Generating code coverage report...');
            $this->runCoverageTests();
        }
        
        // Summary
        if ($featureResult === 0 && $unitResult === 0) {
            $this->info('✅ All tests passed successfully!');
            return 0;
        } else {
            $this->error('❌ Some tests failed. Please check the output above.');
            return 1;
        }
    }
    
    private function runTests($suite)
    {
        $command = "vendor/bin/phpunit --testsuite={$suite} --colors=always";
        
        $process = proc_open($command, [
            0 => ['pipe', 'r'],
            1 => ['pipe', 'w'],
            2 => ['pipe', 'w']
        ], $pipes);
        
        if (is_resource($process)) {
            fclose($pipes[0]);
            
            $output = stream_get_contents($pipes[1]);
            $errors = stream_get_contents($pipes[2]);
            
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            $returnCode = proc_close($process);
            
            $this->line($output);
            if ($errors) {
                $this->error($errors);
            }
            
            return $returnCode;
        }
        
        return 1;
    }
    
    private function runCoverageTests()
    {
        $command = "vendor/bin/phpunit --coverage-html storage/coverage --colors=always";
        
        $process = proc_open($command, [
            0 => ['pipe', 'r'],
            1 => ['pipe', 'w'],
            2 => ['pipe', 'w']
        ], $pipes);
        
        if (is_resource($process)) {
            fclose($pipes[0]);
            
            $output = stream_get_contents($pipes[1]);
            $errors = stream_get_contents($pipes[2]);
            
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            proc_close($process);
            
            $this->line($output);
            if ($errors) {
                $this->error($errors);
            }
            
            $this->info('Coverage report generated in storage/coverage/index.html');
        }
    }
}
