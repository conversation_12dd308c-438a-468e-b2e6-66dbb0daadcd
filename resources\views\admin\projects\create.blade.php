@extends('layouts.admin')

@section('title', 'Add New Project')

@section('content')
<div class="card">
    <div class="card-header">
        <h3>Add New Project</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('admin.projects.store') }}" enctype="multipart/form-data">
            @csrf
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="title">Project Title *</label>
                        <input type="text" id="title" name="title" class="form-control" 
                               value="{{ old('title') }}" required>
                        @error('title')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" class="form-control" rows="5" 
                                  required>{{ old('description') }}</textarea>
                        <small style="color: #666;">Describe what this project is about and the technologies used</small>
                        @error('description')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="url">Project URL</label>
                        <input type="text" id="url" name="url" class="form-control" 
                               value="{{ old('url') }}" placeholder="https://example.com">
                        <small style="color: #666;">Link to live project, GitHub repository, or demo</small>
                        @error('url')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="order">Display Order *</label>
                        <input type="number" id="order" name="order" class="form-control" 
                               value="{{ old('order', 1) }}" min="1" required>
                        <small style="color: #666;">Lower numbers appear first</small>
                        @error('order')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <div class="form-group">
                        <label for="image">Project Image</label>
                        <input type="file" id="image" name="image" class="form-control" accept="image/*">
                        <small style="color: #666;">Upload a screenshot or preview image (JPEG, PNG, JPG, GIF - Max: 2MB)</small>
                        @error('image')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <h5 style="margin-bottom: 20px; color: #333;">Project Settings</h5>
                        
                        <div class="form-group">
                            <label style="display: flex; align-items: center; gap: 10px;">
                                <input type="checkbox" name="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                <span>Featured Project</span>
                            </label>
                            <small style="color: #666;">Featured projects are highlighted and may appear in special sections</small>
                        </div>
                        
                        <div class="form-group">
                            <label style="display: flex; align-items: center; gap: 10px;">
                                <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <span>Active</span>
                            </label>
                            <small style="color: #666;">Uncheck to hide this project from the website</small>
                        </div>
                    </div>
                    
                    <!-- Preview Area -->
                    <div style="margin-top: 30px;">
                        <h6 style="color: #333; margin-bottom: 10px;">Image Preview:</h6>
                        <div id="image-preview" style="width: 100%; height: 200px; border: 2px dashed #ddd; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; background: #f8f9fa;">
                            <span>No image selected</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 30px;">
                <button type="submit" class="btn">Create Project</button>
                <a href="{{ route('admin.projects.index') }}" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('image-preview');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '<span>No image selected</span>';
    }
});
</script>
@endsection
