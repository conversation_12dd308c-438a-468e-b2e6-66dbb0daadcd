<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ServiceController extends Controller
{
    public function index()
    {
        $services = Service::orderBy('order')->get();
        return view('admin.services.index', compact('services'));
    }

    public function create()
    {
        return view('admin.services.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:1024',
            'order' => 'required|integer|min:1',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = [
            'title' => $request->title,
            'slug' => \Str::slug($request->title),
            'description' => $request->description,
            'order' => $request->order,
            'is_active' => $request->boolean('is_active', true),
        ];

        // Handle icon upload
        if ($request->hasFile('icon')) {
            $icon = $request->file('icon');
            $filename = time() . '_service_' . $icon->getClientOriginalName();
            $icon->move(public_path('images/services'), $filename);
            $data['icon'] = 'images/services/' . $filename;
        }

        Service::create($data);

        return redirect()->route('admin.services.index')->with('success', 'Service created successfully!');
    }

    public function show(Service $service)
    {
        return view('admin.services.show', compact('service'));
    }

    public function edit(Service $service)
    {
        return view('admin.services.edit', compact('service'));
    }

    public function update(Request $request, Service $service)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:1024',
            'order' => 'required|integer|min:1',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = [
            'title' => $request->title,
            'slug' => \Str::slug($request->title),
            'description' => $request->description,
            'order' => $request->order,
            'is_active' => $request->boolean('is_active'),
        ];

        // Handle icon upload
        if ($request->hasFile('icon')) {
            // Delete old icon if exists
            if ($service->icon && file_exists(public_path($service->icon))) {
                unlink(public_path($service->icon));
            }

            $icon = $request->file('icon');
            $filename = time() . '_service_' . $icon->getClientOriginalName();
            $icon->move(public_path('images/services'), $filename);
            $data['icon'] = 'images/services/' . $filename;
        }

        $service->update($data);

        return redirect()->route('admin.services.index')->with('success', 'Service updated successfully!');
    }

    public function destroy(Service $service)
    {
        // Delete icon if exists
        if ($service->icon && file_exists(public_path($service->icon))) {
            unlink(public_path($service->icon));
        }

        $service->delete();
        return redirect()->route('admin.services.index')->with('success', 'Service deleted successfully!');
    }

    public function updateOrder(Request $request)
    {
        $items = $request->input('items', []);
        
        foreach ($items as $index => $id) {
            Service::where('id', $id)->update(['order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }

    public function toggle(Service $service)
    {
        $service->update(['is_active' => !$service->is_active]);
        
        return response()->json([
            'success' => true,
            'is_active' => $service->is_active
        ]);
    }
}
