<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Service;
use App\Models\Setting;
use App\Models\NavbarItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class PageController extends Controller
{
    public function projects()
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $navbarItems = NavbarItem::where('is_active', true)->orderBy('order')->get();
        
        $projects = Project::where('is_active', true)
            ->orderBy('order')
            ->paginate(12);
            
        return view('pages.projects', compact('projects', 'settings', 'navbarItems'));
    }
    
    public function projectDetail($id)
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $navbarItems = NavbarItem::where('is_active', true)->orderBy('order')->get();
        
        $project = Project::where('is_active', true)->findOrFail($id);
        $relatedProjects = Project::where('is_active', true)
            ->where('id', '!=', $id)
            ->orderBy('order')
            ->limit(3)
            ->get();
            
        return view('pages.project-detail', compact('project', 'relatedProjects', 'settings', 'navbarItems'));
    }
    
    public function services()
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $navbarItems = NavbarItem::where('is_active', true)->orderBy('order')->get();
        
        $services = Service::where('is_active', true)
            ->orderBy('order')
            ->get();
            
        return view('pages.services', compact('services', 'settings', 'navbarItems'));
    }
    
    public function blog()
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $navbarItems = NavbarItem::where('is_active', true)->orderBy('order')->get();
        
        $posts = $this->fetchBlogPosts();
        
        return view('pages.blog', compact('posts', 'settings', 'navbarItems'));
    }
    
    public function blogPost($slug)
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $navbarItems = NavbarItem::where('is_active', true)->orderBy('order')->get();
        
        // For demo purposes, create a mock post based on slug
        $post = [
            'title' => ucwords(str_replace('-', ' ', $slug)),
            'slug' => $slug,
            'content' => 'This is a detailed blog post about ' . ucwords(str_replace('-', ' ', $slug)) . '. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
            'image' => 'https://via.placeholder.com/800x400',
            'published_at' => now()->format('M d, Y'),
            'author' => 'Aziz Khan',
            'excerpt' => 'A comprehensive guide to ' . ucwords(str_replace('-', ' ', $slug)) . '...'
        ];
        
        $relatedPosts = $this->fetchBlogPosts(3);
        
        return view('pages.blog-post', compact('post', 'relatedPosts', 'settings', 'navbarItems'));
    }
    
    public function contact()
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $navbarItems = NavbarItem::where('is_active', true)->orderBy('order')->get();
        
        return view('pages.contact', compact('settings', 'navbarItems'));
    }
    
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);
        
        // Here you would typically send an email or store in database
        // For now, just return success
        
        return back()->with('success', 'Thank you for your message! I will get back to you soon.');
    }
    
    public function about()
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $navbarItems = NavbarItem::where('is_active', true)->orderBy('order')->get();
        
        return view('pages.about', compact('settings', 'navbarItems'));
    }
    
    public function dynamicPage($slug)
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $navbarItems = NavbarItem::where('is_active', true)->orderBy('order')->get();
        
        // Find navbar item by slug
        $navbarItem = NavbarItem::where('slug', $slug)
            ->where('is_active', true)
            ->first();
            
        if (!$navbarItem) {
            abort(404);
        }
        
        // Check if it's a predefined page
        switch ($slug) {
            case 'projects':
                return $this->projects();
            case 'services':
                return $this->services();
            case 'blog':
                return $this->blog();
            case 'contact':
                return $this->contact();
            case 'about':
                return $this->about();
            default:
                // Generic page
                return view('pages.generic', compact('navbarItem', 'settings', 'navbarItems'));
        }
    }
    
    private function fetchBlogPosts($limit = 10)
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        $apiKey = $settings['blog_api_key'] ?? null;
        $cacheKey = "blog_posts_{$limit}";
        
        if (!$apiKey) {
            return [];
        }
        
        return Cache::remember($cacheKey, 3600, function () use ($apiKey, $limit) {
            try {
                $response = Http::timeout(10)->get('https://newsapi.org/v2/everything', [
                    'q' => 'technology OR programming OR web development',
                    'language' => 'en',
                    'sortBy' => 'publishedAt',
                    'pageSize' => $limit,
                    'apiKey' => $apiKey,
                ]);
                
                if ($response->successful()) {
                    $data = $response->json();
                    return collect($data['articles'] ?? [])->map(function ($article) {
                        return [
                            'title' => $article['title'],
                            'description' => $article['description'],
                            'url' => $article['url'],
                            'image' => $article['urlToImage'] ?? 'https://via.placeholder.com/400x200',
                            'published_at' => date('M d, Y', strtotime($article['publishedAt'])),
                            'source' => $article['source']['name'] ?? 'Unknown',
                            'slug' => \Str::slug($article['title'])
                        ];
                    })->toArray();
                }
            } catch (\Exception $e) {
                \Log::error('Blog API Error: ' . $e->getMessage());
            }
            
            return [];
        });
    }
}
