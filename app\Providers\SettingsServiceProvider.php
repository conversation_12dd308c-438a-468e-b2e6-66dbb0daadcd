<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Helpers\SettingsHelper;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share settings helper with all views
        View::composer('*', function ($view) {
            $view->with('settings_helper', new SettingsHelper());
        });

        // Share common settings with all views
        View::composer('*', function ($view) {
            $view->with([
                'site_colors' => SettingsHelper::getColors(),
                'section_visibility' => SettingsHelper::getSectionVisibility(),
                'content_counts' => SettingsHelper::getContentCounts(),
            ]);
        });
    }
}
