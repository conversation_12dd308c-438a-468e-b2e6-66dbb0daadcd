@extends('layouts.admin')

@section('title', 'Edit Hero Section')

@section('content')
<div class="card">
    <div class="card-header">
        <h3>Edit Hero Section</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('admin.hero.update', $hero) }}" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="name">Name *</label>
                        <input type="text" id="name" name="name" class="form-control" 
                               value="{{ old('name', $hero->name) }}" required>
                        @error('name')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="title">Title/Profession *</label>
                        <input type="text" id="title" name="title" class="form-control" 
                               value="{{ old('title', $hero->title) }}" required>
                        @error('title')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" class="form-control" rows="4" 
                                  required>{{ old('description', $hero->description) }}</textarea>
                        <small style="color: #666;">Brief description about yourself and what you do</small>
                        @error('description')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="photo">Profile Photo</label>
                        @if($hero->photo)
                            <div style="margin-bottom: 10px;">
                                <img src="{{ asset($hero->photo) }}" alt="Current photo" 
                                     style="width: 100px; height: 100px; object-fit: cover; border-radius: 50%; border: 2px solid #ddd;">
                                <p style="font-size: 14px; color: #666; margin-top: 5px;">Current photo</p>
                            </div>
                        @endif
                        <input type="file" id="photo" name="photo" class="form-control" accept="image/*">
                        <small style="color: #666;">Upload a new photo to replace the current one (JPEG, PNG, JPG, GIF - Max: 2MB)</small>
                        @error('photo')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <h5 style="margin-bottom: 20px; color: #333;">Call-to-Action Buttons</h5>
                    
                    <div class="form-group">
                        <label for="cta_primary_text">Primary Button Text</label>
                        <input type="text" id="cta_primary_text" name="cta_primary_text" class="form-control" 
                               value="{{ old('cta_primary_text', $hero->cta_primary_text) }}">
                        @error('cta_primary_text')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_primary_url">Primary Button URL</label>
                        <input type="text" id="cta_primary_url" name="cta_primary_url" class="form-control" 
                               value="{{ old('cta_primary_url', $hero->cta_primary_url) }}">
                        @error('cta_primary_url')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_secondary_text">Secondary Button Text</label>
                        <input type="text" id="cta_secondary_text" name="cta_secondary_text" class="form-control" 
                               value="{{ old('cta_secondary_text', $hero->cta_secondary_text) }}">
                        @error('cta_secondary_text')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_secondary_url">Secondary Button URL</label>
                        <input type="text" id="cta_secondary_url" name="cta_secondary_url" class="form-control" 
                               value="{{ old('cta_secondary_url', $hero->cta_secondary_url) }}">
                        @error('cta_secondary_url')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" name="show_galaxy_animation" value="1" 
                                   {{ old('show_galaxy_animation', $hero->show_galaxy_animation) ? 'checked' : '' }}>
                            <span>Enable Galaxy Star Animation</span>
                        </label>
                        <small style="color: #666;">Show animated stars in the background of the hero section</small>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 30px;">
                <button type="submit" class="btn">Update Hero Section</button>
                <a href="{{ route('admin.hero.index') }}" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>
@endsection
