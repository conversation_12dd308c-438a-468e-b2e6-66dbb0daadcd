<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\SiteSetting;
use App\Models\NavbarItem;
use App\Models\HeroSection;
use App\Models\ProjectStat;
use Illuminate\Support\Facades\Hash;

class InitialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Aziz Khan',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create default site settings
        $defaultSettings = [
            // Colors
            ['key' => 'primary_color', 'value' => '#3b82f6', 'type' => 'color', 'group' => 'colors'],
            ['key' => 'secondary_color', 'value' => '#64748b', 'type' => 'color', 'group' => 'colors'],
            ['key' => 'background_color', 'value' => '#ffffff', 'type' => 'color', 'group' => 'colors'],
            ['key' => 'text_color', 'value' => '#1f2937', 'type' => 'color', 'group' => 'colors'],
            
            // Section visibility
            ['key' => 'show_navbar', 'value' => '1', 'type' => 'boolean', 'group' => 'sections'],
            ['key' => 'show_hero', 'value' => '1', 'type' => 'boolean', 'group' => 'sections'],
            ['key' => 'show_services', 'value' => '1', 'type' => 'boolean', 'group' => 'sections'],
            ['key' => 'show_projects', 'value' => '1', 'type' => 'boolean', 'group' => 'sections'],
            ['key' => 'show_stats', 'value' => '1', 'type' => 'boolean', 'group' => 'sections'],
            ['key' => 'show_blog', 'value' => '1', 'type' => 'boolean', 'group' => 'sections'],
            ['key' => 'show_testimonials', 'value' => '1', 'type' => 'boolean', 'group' => 'sections'],
            ['key' => 'show_footer', 'value' => '1', 'type' => 'boolean', 'group' => 'sections'],
            
            // Content counts
            ['key' => 'homepage_projects_count', 'value' => '6', 'type' => 'number', 'group' => 'content'],
            ['key' => 'homepage_services_count', 'value' => '6', 'type' => 'number', 'group' => 'content'],
            ['key' => 'homepage_blog_count', 'value' => '6', 'type' => 'number', 'group' => 'content'],
            
            // Blog settings
            ['key' => 'blog_title', 'value' => 'Latest Tech News', 'type' => 'text', 'group' => 'blog'],
            ['key' => 'blog_description', 'value' => 'Stay updated with the latest technology trends and news', 'type' => 'text', 'group' => 'blog'],
            
            // Projects section
            ['key' => 'projects_title', 'value' => 'My Projects', 'type' => 'text', 'group' => 'projects'],
            ['key' => 'projects_description', 'value' => 'Here are some of my recent projects', 'type' => 'text', 'group' => 'projects'],
            
            // Services section
            ['key' => 'services_title', 'value' => 'My Services', 'type' => 'text', 'group' => 'services'],
            ['key' => 'services_description', 'value' => 'What I can do for you', 'type' => 'text', 'group' => 'services'],
            
            // Testimonials section
            ['key' => 'testimonials_title', 'value' => 'What Clients Say', 'type' => 'text', 'group' => 'testimonials'],
            ['key' => 'testimonials_description', 'value' => 'Feedback from satisfied clients', 'type' => 'text', 'group' => 'testimonials'],
            
            // Footer
            ['key' => 'footer_description', 'value' => 'Professional 2D/3D Artist and Web Developer creating amazing digital experiences.', 'type' => 'text', 'group' => 'footer'],
        ];

        foreach ($defaultSettings as $setting) {
            SiteSetting::create($setting);
        }

        // Create default navbar items
        $navbarItems = [
            ['title' => 'Home', 'slug' => 'home', 'url' => '/', 'order' => 1],
            ['title' => 'Services', 'slug' => 'services', 'url' => '/services', 'order' => 2, 'auto_generated' => true],
            ['title' => 'Projects', 'slug' => 'projects', 'url' => '/projects', 'order' => 3, 'auto_generated' => true],
            ['title' => 'Blog', 'slug' => 'blog', 'url' => '/blog', 'order' => 4, 'auto_generated' => true],
            ['title' => 'Contact', 'slug' => 'contact', 'url' => '/contact', 'order' => 5, 'auto_generated' => true],
        ];

        foreach ($navbarItems as $item) {
            NavbarItem::create($item);
        }

        // Create default hero section
        HeroSection::create([
            'name' => 'Aziz Khan',
            'title' => '2D/3D Artist and Web Developer',
            'description' => 'Creating amazing digital experiences with modern web technologies and stunning visual designs.',
            'photo' => 'portfolio.jpg',
            'cta_button_1_text' => 'View Projects',
            'cta_button_1_link' => '/projects',
            'cta_button_2_text' => "Let's Get Started",
            'cta_button_2_link' => '/contact',
            'galaxy_animation' => true,
            'is_active' => true,
        ]);

        // Create default project stats
        $projectStats = [
            ['label' => 'Web Design/Development', 'count' => 50, 'order' => 1],
            ['label' => '3D Works', 'count' => 30, 'order' => 2],
            ['label' => 'Graphic Works', 'count' => 75, 'order' => 3],
            ['label' => 'Ongoing Projects', 'count' => 12, 'order' => 4],
        ];

        foreach ($projectStats as $stat) {
            ProjectStat::create($stat);
        }
    }
}
