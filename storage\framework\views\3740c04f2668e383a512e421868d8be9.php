<?php $__env->startSection('title', 'Edit Hero Section'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3>Edit Hero Section</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="<?php echo e(route('admin.hero.update', $hero)); ?>" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="name">Name *</label>
                        <input type="text" id="name" name="name" class="form-control" 
                               value="<?php echo e(old('name', $hero->name)); ?>" required>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="title">Title/Profession *</label>
                        <input type="text" id="title" name="title" class="form-control" 
                               value="<?php echo e(old('title', $hero->title)); ?>" required>
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" class="form-control" rows="4" 
                                  required><?php echo e(old('description', $hero->description)); ?></textarea>
                        <small style="color: #666;">Brief description about yourself and what you do</small>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="photo">Profile Photo</label>
                        <?php if($hero->photo): ?>
                            <div style="margin-bottom: 10px;">
                                <img src="<?php echo e(asset($hero->photo)); ?>" alt="Current photo" 
                                     style="width: 100px; height: 100px; object-fit: cover; border-radius: 50%; border: 2px solid #ddd;">
                                <p style="font-size: 14px; color: #666; margin-top: 5px;">Current photo</p>
                            </div>
                        <?php endif; ?>
                        <input type="file" id="photo" name="photo" class="form-control" accept="image/*">
                        <small style="color: #666;">Upload a new photo to replace the current one (JPEG, PNG, JPG, GIF - Max: 2MB)</small>
                        <?php $__errorArgs = ['photo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <h5 style="margin-bottom: 20px; color: #333;">Call-to-Action Buttons</h5>
                    
                    <div class="form-group">
                        <label for="cta_primary_text">Primary Button Text</label>
                        <input type="text" id="cta_primary_text" name="cta_primary_text" class="form-control" 
                               value="<?php echo e(old('cta_primary_text', $hero->cta_primary_text)); ?>">
                        <?php $__errorArgs = ['cta_primary_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_primary_url">Primary Button URL</label>
                        <input type="text" id="cta_primary_url" name="cta_primary_url" class="form-control" 
                               value="<?php echo e(old('cta_primary_url', $hero->cta_primary_url)); ?>">
                        <?php $__errorArgs = ['cta_primary_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_secondary_text">Secondary Button Text</label>
                        <input type="text" id="cta_secondary_text" name="cta_secondary_text" class="form-control" 
                               value="<?php echo e(old('cta_secondary_text', $hero->cta_secondary_text)); ?>">
                        <?php $__errorArgs = ['cta_secondary_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_secondary_url">Secondary Button URL</label>
                        <input type="text" id="cta_secondary_url" name="cta_secondary_url" class="form-control" 
                               value="<?php echo e(old('cta_secondary_url', $hero->cta_secondary_url)); ?>">
                        <?php $__errorArgs = ['cta_secondary_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" name="show_galaxy_animation" value="1" 
                                   <?php echo e(old('show_galaxy_animation', $hero->show_galaxy_animation) ? 'checked' : ''); ?>>
                            <span>Enable Galaxy Star Animation</span>
                        </label>
                        <small style="color: #666;">Show animated stars in the background of the hero section</small>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 30px;">
                <button type="submit" class="btn">Update Hero Section</button>
                <a href="<?php echo e(route('admin.hero.index')); ?>" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\hero\edit.blade.php ENDPATH**/ ?>