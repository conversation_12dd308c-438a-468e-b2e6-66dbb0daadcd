<?php

namespace Database\Factories;

use App\Models\ProjectStat;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProjectStatFactory extends Factory
{
    protected $model = ProjectStat::class;

    public function definition()
    {
        return [
            'title' => $this->faker->randomElement([
                'Projects Completed',
                'Happy Clients',
                'Years Experience',
                'Technologies Mastered',
                'Lines of Code',
                'Coffee Cups'
            ]),
            'value' => $this->faker->numberBetween(10, 500),
            'suffix' => $this->faker->randomElement(['', '+', 'K', 'M']),
            'icon' => $this->faker->randomElement([
                'fas fa-project-diagram',
                'fas fa-users',
                'fas fa-calendar',
                'fas fa-code',
                'fas fa-coffee'
            ]),
            'order' => $this->faker->numberBetween(1, 100),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
