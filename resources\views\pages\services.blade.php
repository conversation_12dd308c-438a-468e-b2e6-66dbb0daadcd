@extends('layouts.page')

@section('title', 'Services - <PERSON>')
@section('description', 'Professional web development, design, and creative services offered by <PERSON>.')

@section('content')
<div class="section" style="padding-top: 6rem;">
    <div class="container">
        <!-- Page Header -->
        <div class="text-center mb-2xl">
            <h1 class="text-5xl font-bold mb-lg">My Services</h1>
            <p class="text-xl text-muted max-w-2xl mx-auto">
                I offer comprehensive digital solutions to help bring your ideas to life. 
                From concept to completion, I'm here to help you succeed.
            </p>
        </div>
        
        <!-- Services Grid -->
        <div class="grid grid-auto-fit gap-xl">
            @forelse($services as $service)
                <div class="card hover-lift text-center">
                    <div class="service-icon">
                        @if($service->icon)
                            <img src="{{ asset('images/services/' . $service->icon) }}" 
                                 alt="{{ $service->title }}" 
                                 class="w-16 h-16 mx-auto mb-lg">
                        @else
                            <div class="w-16 h-16 mx-auto mb-lg bg-gradient-primary rounded-full flex items-center justify-center text-white text-2xl">
                                ⚡
                            </div>
                        @endif
                    </div>
                    
                    <h3 class="card-title text-xl">{{ $service->title }}</h3>
                    <p class="card-description mb-lg">{{ $service->description }}</p>
                    
                    @if($service->features)
                        <ul class="text-left mb-lg space-y-2">
                            @foreach(explode("\n", $service->features) as $feature)
                                @if(trim($feature))
                                    <li class="flex items-start gap-2">
                                        <span class="text-primary mt-1">✓</span>
                                        <span class="text-sm">{{ trim($feature) }}</span>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    @endif
                    
                    <a href="{{ url('/contact') }}" class="btn btn-primary w-full">
                        Get Started
                    </a>
                </div>
            @empty
                <div class="col-span-full text-center py-3xl">
                    <div class="text-6xl mb-lg">🛠️</div>
                    <h3 class="text-2xl font-semibold mb-md">Services Coming Soon</h3>
                    <p class="text-muted">Service offerings will be displayed here once they are added.</p>
                </div>
            @endforelse
        </div>
        
        <!-- Process Section -->
        <div class="mt-3xl">
            <h2 class="text-3xl font-bold text-center mb-2xl">My Process</h2>
            <div class="grid md:grid-cols-4 gap-xl">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-lg bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        1
                    </div>
                    <h3 class="font-semibold mb-md">Discovery</h3>
                    <p class="text-sm text-muted">Understanding your needs, goals, and vision for the project.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-lg bg-secondary rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        2
                    </div>
                    <h3 class="font-semibold mb-md">Planning</h3>
                    <p class="text-sm text-muted">Creating a detailed roadmap and strategy for your project.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-lg bg-accent rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        3
                    </div>
                    <h3 class="font-semibold mb-md">Development</h3>
                    <p class="text-sm text-muted">Building your solution with clean code and best practices.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-lg bg-gradient-primary rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        4
                    </div>
                    <h3 class="font-semibold mb-md">Delivery</h3>
                    <p class="text-sm text-muted">Testing, optimization, and launching your finished project.</p>
                </div>
            </div>
        </div>
        
        <!-- Technologies Section -->
        <div class="mt-3xl">
            <h2 class="text-3xl font-bold text-center mb-2xl">Technologies I Use</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-lg">
                @php
                $technologies = [
                    ['name' => 'Laravel', 'icon' => '🔥'],
                    ['name' => 'Vue.js', 'icon' => '💚'],
                    ['name' => 'React', 'icon' => '⚛️'],
                    ['name' => 'PHP', 'icon' => '🐘'],
                    ['name' => 'JavaScript', 'icon' => '⚡'],
                    ['name' => 'MySQL', 'icon' => '🗄️'],
                    ['name' => 'HTML5', 'icon' => '🌐'],
                    ['name' => 'CSS3', 'icon' => '🎨'],
                    ['name' => 'Figma', 'icon' => '🎯'],
                    ['name' => 'Photoshop', 'icon' => '🖼️'],
                    ['name' => 'Blender', 'icon' => '🎭'],
                    ['name' => 'Git', 'icon' => '📝']
                ];
                @endphp
                
                @foreach($technologies as $tech)
                    <div class="text-center p-lg border rounded-lg hover:shadow-md transition">
                        <div class="text-3xl mb-sm">{{ $tech['icon'] }}</div>
                        <div class="text-sm font-medium">{{ $tech['name'] }}</div>
                    </div>
                @endforeach
            </div>
        </div>
        
        <!-- Call to Action -->
        <div class="text-center mt-3xl">
            <div class="bg-gradient-primary rounded-2xl p-3xl text-white">
                <h2 class="text-3xl font-bold mb-lg">Ready to Start Your Project?</h2>
                <p class="text-xl mb-xl opacity-90">
                    Let's discuss how I can help bring your vision to life.
                </p>
                <div class="flex flex-col sm:flex-row gap-md justify-center">
                    <a href="{{ url('/contact') }}" class="btn btn-secondary">
                        Get a Quote
                    </a>
                    <a href="{{ url('/projects') }}" class="btn" style="background: rgba(255,255,255,0.2); color: white;">
                        View My Work
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.w-16 {
    width: 4rem;
}

.h-16 {
    height: 4rem;
}

.w-full {
    width: 100%;
}

.max-w-2xl {
    max-width: 42rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.col-span-full {
    grid-column: 1 / -1;
}

@media (min-width: 768px) {
    .md\:grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-6 {
        grid-template-columns: repeat(6, 1fr);
    }
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}
</style>
@endpush
