<?php $__env->startSection('title', 'Edit Service'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3>Edit Service: <?php echo e($service->title); ?></h3>
    </div>
    <div class="card-body">
        <form method="POST" action="<?php echo e(route('admin.services.update', $service)); ?>" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="title">Service Title *</label>
                        <input type="text" id="title" name="title" class="form-control" 
                               value="<?php echo e(old('title', $service->title)); ?>" required>
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" class="form-control" rows="6" 
                                  required><?php echo e(old('description', $service->description)); ?></textarea>
                        <small style="color: #666;">Describe what this service includes and how it benefits clients</small>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="order">Display Order *</label>
                        <input type="number" id="order" name="order" class="form-control" 
                               value="<?php echo e(old('order', $service->order)); ?>" min="1" required>
                        <small style="color: #666;">Lower numbers appear first</small>
                        <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" name="is_active" value="1" 
                                   <?php echo e(old('is_active', $service->is_active) ? 'checked' : ''); ?>>
                            <span>Active</span>
                        </label>
                        <small style="color: #666;">Uncheck to hide this service from the website</small>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <div class="form-group">
                        <label for="icon">Service Icon</label>
                        <?php if($service->icon): ?>
                            <div style="margin-bottom: 10px; text-align: center;">
                                <img src="<?php echo e(asset($service->icon)); ?>" alt="Current icon" 
                                     style="width: 60px; height: 60px; object-fit: contain; border-radius: 50%; border: 2px solid #ddd;">
                                <p style="font-size: 14px; color: #666; margin-top: 5px;">Current icon</p>
                            </div>
                        <?php endif; ?>
                        <input type="file" id="icon" name="icon" class="form-control" accept="image/*">
                        <small style="color: #666;">Upload a new icon to replace the current one (JPEG, PNG, JPG, GIF, SVG - Max: 1MB)</small>
                        <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <!-- Preview Area -->
                    <div style="margin-top: 30px;">
                        <h6 style="color: #333; margin-bottom: 10px;">New Icon Preview:</h6>
                        <div id="icon-preview" style="width: 100px; height: 100px; border: 2px dashed #ddd; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #666; background: #f8f9fa; margin: 0 auto;">
                            <span style="font-size: 14px;">No new icon</span>
                        </div>
                    </div>
                    
                    <!-- Service Preview -->
                    <div style="margin-top: 30px;">
                        <h6 style="color: #333; margin-bottom: 10px;">Service Preview:</h6>
                        <div style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 20px; text-align: center;">
                            <div style="width: 60px; height: 60px; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border-radius: 50%; font-size: 24px; color: #667eea;">
                                <?php if($service->icon): ?>
                                    <img src="<?php echo e(asset($service->icon)); ?>" alt="<?php echo e($service->title); ?>" 
                                         style="width: 30px; height: 30px; object-fit: contain;" id="preview-icon">
                                <?php else: ?>
                                    <span id="preview-icon">🔧</span>
                                <?php endif; ?>
                            </div>
                            <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px; color: #333;" id="preview-title"><?php echo e($service->title); ?></div>
                            <div style="color: #666; font-size: 14px; line-height: 1.5;" id="preview-description"><?php echo e($service->description); ?></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 30px;">
                <button type="submit" class="btn">Update Service</button>
                <a href="<?php echo e(route('admin.services.index')); ?>" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>

<script>
// Update preview when form fields change
document.getElementById('title').addEventListener('input', function() {
    document.getElementById('preview-title').textContent = this.value || '<?php echo e($service->title); ?>';
});

document.getElementById('description').addEventListener('input', function() {
    document.getElementById('preview-description').textContent = this.value || '<?php echo e($service->description); ?>';
});

document.getElementById('icon').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('icon-preview');
    const previewIcon = document.getElementById('preview-icon');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" style="width: 60px; height: 60px; object-fit: contain; border-radius: 50%;">`;
            previewIcon.outerHTML = `<img src="${e.target.result}" style="width: 30px; height: 30px; object-fit: contain;" id="preview-icon">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '<span style="font-size: 14px;">No new icon</span>';
        <?php if($service->icon): ?>
            previewIcon.outerHTML = `<img src="<?php echo e(asset($service->icon)); ?>" alt="<?php echo e($service->title); ?>" style="width: 30px; height: 30px; object-fit: contain;" id="preview-icon">`;
        <?php else: ?>
            previewIcon.outerHTML = '<span id="preview-icon">🔧</span>';
        <?php endif; ?>
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\services\edit.blade.php ENDPATH**/ ?>