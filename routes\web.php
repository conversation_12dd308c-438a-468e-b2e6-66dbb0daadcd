<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Auth\VerificationController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\NavbarController;
use App\Http\Controllers\Admin\HeroController;
use App\Http\Controllers\Admin\ProjectController;
use App\Http\Controllers\Admin\ServiceController;
use App\Http\Controllers\Admin\ProjectStatsController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\TestimonialController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\DynamicStyleController;
use App\Http\Controllers\PageController;

// Public routes
Route::get('/', [HomeController::class, 'index']);
Route::get('/css/dynamic-styles.css', [DynamicStyleController::class, 'generateCSS'])->name('dynamic.styles');

// Standalone pages
Route::get('/projects', [PageController::class, 'projects'])->name('projects');
Route::get('/project/{id}', [PageController::class, 'projectDetail'])->name('project.detail');
Route::get('/services', [PageController::class, 'services'])->name('services');
Route::get('/blog', [PageController::class, 'blog'])->name('blog');
Route::get('/blog/{slug}', [PageController::class, 'blogPost'])->name('blog.post');
Route::get('/contact', [PageController::class, 'contact'])->name('contact');
Route::post('/contact', [PageController::class, 'submitContact'])
    ->middleware('rate.limit:contact,5,1')
    ->name('contact.submit');
Route::get('/about', [PageController::class, 'about'])->name('about');

// Dynamic pages (catch-all for navbar items)
Route::get('/{slug}', [PageController::class, 'dynamicPage'])->name('page.dynamic');

// Dynamic CSS route
Route::get('/css/dynamic-colors.css', [SettingsController::class, 'generateCss'])->name('dynamic.css');

// Test settings route (for development)
Route::get('/test-settings', function () {
    return view('test-settings');
})->name('test.settings');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);

    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);

    Route::get('/forgot-password', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');

    Route::get('/reset-password/{token}', [ResetPasswordController::class, 'showResetForm'])->name('password.reset');
    Route::post('/reset-password', [ResetPasswordController::class, 'reset'])->name('password.update');
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

    Route::get('/email/verify', [VerificationController::class, 'show'])->name('verification.notice');
    Route::get('/email/verify/{id}/{hash}', [VerificationController::class, 'verify'])->middleware('signed')->name('verification.verify');
    Route::post('/email/verification-notification', [VerificationController::class, 'resend'])->middleware('throttle:6,1')->name('verification.send');
});

// Admin routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Placeholder routes for admin sections (will be implemented in subsequent tasks)
    Route::resource('hero', HeroController::class);
    Route::post('/hero/{hero}/activate', [HeroController::class, 'activate'])->name('hero.activate');
    Route::resource('projects', ProjectController::class);
    Route::post('/projects/order', [ProjectController::class, 'updateOrder'])->name('projects.order');
    Route::post('/projects/{project}/toggle', [ProjectController::class, 'toggle'])->name('projects.toggle');
    Route::post('/projects/{project}/toggle-featured', [ProjectController::class, 'toggleFeatured'])->name('projects.toggle-featured');
    Route::get('/projects/create', function () {
        return view('admin.coming-soon', ['section' => 'Create Project']);
    })->name('projects.create');
    Route::resource('services', ServiceController::class);
    Route::post('/services/order', [ServiceController::class, 'updateOrder'])->name('services.order');
    Route::post('/services/{service}/toggle', [ServiceController::class, 'toggle'])->name('services.toggle');
    Route::get('/services/create', function () {
        return view('admin.coming-soon', ['section' => 'Create Service']);
    })->name('services.create');
    Route::resource('testimonials', TestimonialController::class);
    Route::post('/testimonials/order', [TestimonialController::class, 'updateOrder'])->name('testimonials.order');
    Route::post('/testimonials/{testimonial}/toggle', [TestimonialController::class, 'toggle'])->name('testimonials.toggle');
    Route::resource('project-stats', ProjectStatsController::class);
    Route::post('/project-stats/order', [ProjectStatsController::class, 'updateOrder'])->name('project-stats.order');
    Route::post('/project-stats/{projectStat}/toggle', [ProjectStatsController::class, 'toggle'])->name('project-stats.toggle');
    Route::post('/project-stats/{projectStat}/increment', [ProjectStatsController::class, 'incrementCount'])->name('project-stats.increment');
    Route::post('/project-stats/{projectStat}/decrement', [ProjectStatsController::class, 'decrementCount'])->name('project-stats.decrement');
    Route::resource('navbar', NavbarController::class);
    Route::post('/navbar/order', [NavbarController::class, 'updateOrder'])->name('navbar.order');
    Route::post('/navbar/{navbar}/toggle', [NavbarController::class, 'toggle'])->name('navbar.toggle');
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::post('/settings/colors', [SettingsController::class, 'updateColors'])->name('settings.colors');
    Route::post('/settings/sections', [SettingsController::class, 'updateSections'])->name('settings.sections');
    Route::post('/settings/content', [SettingsController::class, 'updateContent'])->name('settings.content');
    Route::post('/settings/titles', [SettingsController::class, 'updateSectionTitles'])->name('settings.titles');
    Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
    Route::get('/blog/settings', [BlogController::class, 'settings'])->name('blog.settings');
    Route::post('/blog/settings', [BlogController::class, 'updateSettings'])->name('blog.settings.update');
    Route::post('/blog/test', [BlogController::class, 'testConnection'])->name('blog.test');
    Route::post('/blog/refresh', [BlogController::class, 'refreshPosts'])->name('blog.refresh');
    Route::get('/users', function () {
        return view('admin.coming-soon', ['section' => 'Users']);
    })->name('users.index');
});
