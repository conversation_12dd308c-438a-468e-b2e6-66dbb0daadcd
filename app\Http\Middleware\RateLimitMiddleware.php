<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Response;

class RateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $key
     * @param  int  $maxAttempts
     * @param  int  $decayMinutes
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $key = 'global', $maxAttempts = 60, $decayMinutes = 1)
    {
        $rateLimitKey = $this->resolveRequestSignature($request, $key);
        
        if (RateLimiter::tooManyAttempts($rateLimitKey, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($rateLimitKey);
            
            return response()->json([
                'message' => 'Too many requests. Please try again later.',
                'retry_after' => $seconds
            ], Response::HTTP_TOO_MANY_REQUESTS)
            ->header('Retry-After', $seconds)
            ->header('X-RateLimit-Limit', $maxAttempts)
            ->header('X-RateLimit-Remaining', 0);
        }
        
        RateLimiter::hit($rateLimitKey, $decayMinutes * 60);
        
        $response = $next($request);
        
        $remaining = RateLimiter::remaining($rateLimitKey, $maxAttempts);
        
        return $response
            ->header('X-RateLimit-Limit', $maxAttempts)
            ->header('X-RateLimit-Remaining', $remaining);
    }
    
    /**
     * Resolve the rate limiting signature for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $key
     * @return string
     */
    protected function resolveRequestSignature(Request $request, $key)
    {
        if ($key === 'ip') {
            return 'rate_limit:' . $request->ip();
        }
        
        if ($key === 'user' && $request->user()) {
            return 'rate_limit:user:' . $request->user()->id;
        }
        
        return 'rate_limit:' . $key . ':' . $request->ip();
    }
}
