@extends('layouts.admin')

@section('title', 'Services Management')

@section('content')
<style>
    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .services-grid.ui-sortable .service-card {
        cursor: move;
    }

    .service-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        transition: all 0.3s ease;
        text-align: center;
    }

    .service-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .service-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 50%;
        font-size: 24px;
        color: #667eea;
    }

    .service-icon img {
        width: 40px;
        height: 40px;
        object-fit: contain;
    }

    .service-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
    }

    .service-description {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 15px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .service-meta {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-bottom: 15px;
        font-size: 12px;
    }

    .badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 500;
    }

    .badge-active {
        background-color: #d4edda;
        color: #155724;
    }

    .badge-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }

    .service-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 20px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #28a745;
    }

    input:checked + .slider:before {
        transform: translateX(20px);
    }
</style>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Services ({{ $services->count() }})</h3>
            <a href="{{ route('admin.services.create') }}" class="btn btn-success">Add New Service</a>
        </div>
    </div>
    <div class="card-body">
        @if($services->count() > 0)
            <p style="color: #666; margin-bottom: 20px;">
                <strong>Tip:</strong> Drag and drop services to reorder them. Use the toggle switch to control visibility.
            </p>
            
            <div id="sortable-services" class="services-grid">
                @foreach($services as $service)
                <div class="service-card" data-id="{{ $service->id }}">
                    <div class="service-icon">
                        @if($service->icon)
                            <img src="{{ asset($service->icon) }}" alt="{{ $service->title }}">
                        @else
                            🔧
                        @endif
                    </div>
                    
                    <div class="service-title">{{ $service->title }}</div>
                    <div class="service-description">{{ $service->description }}</div>
                    
                    <div class="service-meta">
                        <span>Order: {{ $service->order }}</span>
                        <span class="badge {{ $service->is_active ? 'badge-active' : 'badge-inactive' }}">
                            {{ $service->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    
                    <div class="service-actions">
                        <label class="toggle-switch" title="Toggle Active">
                            <input type="checkbox" {{ $service->is_active ? 'checked' : '' }} 
                                   onchange="toggleService({{ $service->id }}, this)">
                            <span class="slider"></span>
                        </label>
                        
                        <div style="display: flex; gap: 5px;">
                            <a href="{{ route('admin.services.edit', $service) }}" class="btn btn-sm">Edit</a>
                            <form method="POST" action="{{ route('admin.services.destroy', $service) }}" 
                                  style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div style="text-align: center; padding: 40px 20px;">
                <div style="font-size: 48px; color: #667eea; margin-bottom: 20px;">🛠️</div>
                <h4 style="color: #333; margin-bottom: 15px;">No Services Found</h4>
                <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                    Start showcasing your skills by adding your first service.
                </p>
                <a href="{{ route('admin.services.create') }}" class="btn">Add First Service</a>
            </div>
        @endif
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    $("#sortable-services").sortable({
        update: function(event, ui) {
            var order = $(this).sortable('toArray', {attribute: 'data-id'});
            
            $.ajax({
                url: '{{ route("admin.services.order") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    items: order
                },
                success: function(response) {
                    if (response.success) {
                        // Update order numbers in the UI
                        $('#sortable-services .service-card').each(function(index) {
                            $(this).find('.service-meta span:first').text('Order: ' + (index + 1));
                        });
                    }
                },
                error: function() {
                    alert('Error updating order. Please refresh the page.');
                }
            });
        }
    });
});

function toggleService(id, checkbox) {
    $.ajax({
        url: '/admin/services/' + id + '/toggle',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                // Update badge
                const badge = checkbox.closest('.service-card').querySelector('.badge-active, .badge-inactive');
                if (response.is_active) {
                    badge.className = 'badge badge-active';
                    badge.textContent = 'Active';
                } else {
                    badge.className = 'badge badge-inactive';
                    badge.textContent = 'Inactive';
                }
            }
        },
        error: function() {
            checkbox.checked = !checkbox.checked;
            alert('Error updating service status. Please try again.');
        }
    });
}
</script>
@endsection
