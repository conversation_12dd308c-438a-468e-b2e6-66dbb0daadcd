<?php $__env->startSection('title', 'Testimonials Management'); ?>

<?php $__env->startSection('content'); ?>
<style>
    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .testimonial-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        transition: all 0.3s ease;
        cursor: move;
    }

    .testimonial-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .testimonial-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
    }

    .testimonial-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #667eea;
        overflow: hidden;
    }

    .testimonial-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .testimonial-info h4 {
        margin: 0 0 5px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
    }

    .testimonial-role {
        font-size: 14px;
        color: #666;
        margin: 0;
    }

    .testimonial-quote {
        font-style: italic;
        color: #555;
        line-height: 1.6;
        margin-bottom: 15px;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .testimonial-rating {
        display: flex;
        gap: 2px;
        margin-bottom: 15px;
    }

    .star {
        color: #ffc107;
        font-size: 16px;
    }

    .star.empty {
        color: #e9ecef;
    }

    .testimonial-meta {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-bottom: 15px;
        font-size: 12px;
    }

    .badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 500;
    }

    .badge-active {
        background-color: #d4edda;
        color: #155724;
    }

    .badge-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }

    .testimonial-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 20px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #28a745;
    }

    input:checked + .slider:before {
        transform: translateX(20px);
    }
</style>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Testimonials (<?php echo e($testimonials->count()); ?>)</h3>
            <a href="<?php echo e(route('admin.testimonials.create')); ?>" class="btn btn-success">Add New Testimonial</a>
        </div>
    </div>
    <div class="card-body">
        <?php if($testimonials->count() > 0): ?>
            <p style="color: #666; margin-bottom: 20px;">
                <strong>Tip:</strong> Drag and drop testimonials to reorder them. Use the toggle switch to control visibility.
            </p>
            
            <div id="sortable-testimonials" class="testimonials-grid">
                <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="testimonial-card" data-id="<?php echo e($testimonial->id); ?>">
                    <div class="testimonial-header">
                        <div class="testimonial-avatar">
                            <?php if($testimonial->image): ?>
                                <img src="<?php echo e(asset($testimonial->image)); ?>" alt="<?php echo e($testimonial->name); ?>">
                            <?php else: ?>
                                👤
                            <?php endif; ?>
                        </div>
                        <div class="testimonial-info">
                            <h4><?php echo e($testimonial->name); ?></h4>
                            <p class="testimonial-role">
                                <?php echo e($testimonial->role); ?>

                                <?php if($testimonial->company): ?>
                                    at <?php echo e($testimonial->company); ?>

                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    
                    <div class="testimonial-quote">"<?php echo e($testimonial->quote); ?>"</div>
                    
                    <div class="testimonial-rating">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <span class="star <?php echo e($i <= $testimonial->rating ? '' : 'empty'); ?>">★</span>
                        <?php endfor; ?>
                    </div>
                    
                    <div class="testimonial-meta">
                        <span>Order: <?php echo e($testimonial->order); ?></span>
                        <span class="badge <?php echo e($testimonial->is_active ? 'badge-active' : 'badge-inactive'); ?>">
                            <?php echo e($testimonial->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                    </div>
                    
                    <div class="testimonial-actions">
                        <label class="toggle-switch" title="Toggle Active">
                            <input type="checkbox" <?php echo e($testimonial->is_active ? 'checked' : ''); ?> 
                                   onchange="toggleTestimonial(<?php echo e($testimonial->id); ?>, this)">
                            <span class="slider"></span>
                        </label>
                        
                        <div style="display: flex; gap: 5px;">
                            <a href="<?php echo e(route('admin.testimonials.edit', $testimonial)); ?>" class="btn btn-sm">Edit</a>
                            <form method="POST" action="<?php echo e(route('admin.testimonials.destroy', $testimonial)); ?>" 
                                  style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: 40px 20px;">
                <div style="font-size: 48px; color: #667eea; margin-bottom: 20px;">💬</div>
                <h4 style="color: #333; margin-bottom: 15px;">No Testimonials Found</h4>
                <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                    Start building trust by adding client testimonials and reviews.
                </p>
                <a href="<?php echo e(route('admin.testimonials.create')); ?>" class="btn">Add First Testimonial</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    $("#sortable-testimonials").sortable({
        update: function(event, ui) {
            var order = $(this).sortable('toArray', {attribute: 'data-id'});
            
            $.ajax({
                url: '<?php echo e(route("admin.testimonials.order")); ?>',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    items: order
                },
                success: function(response) {
                    if (response.success) {
                        // Update order numbers in the UI
                        $('#sortable-testimonials .testimonial-card').each(function(index) {
                            $(this).find('.testimonial-meta span:first').text('Order: ' + (index + 1));
                        });
                    }
                },
                error: function() {
                    alert('Error updating order. Please refresh the page.');
                }
            });
        }
    });
});

function toggleTestimonial(id, checkbox) {
    $.ajax({
        url: '/admin/testimonials/' + id + '/toggle',
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                // Update badge
                const badge = checkbox.closest('.testimonial-card').querySelector('.badge-active, .badge-inactive');
                if (response.is_active) {
                    badge.className = 'badge badge-active';
                    badge.textContent = 'Active';
                } else {
                    badge.className = 'badge badge-inactive';
                    badge.textContent = 'Inactive';
                }
            }
        },
        error: function() {
            checkbox.checked = !checkbox.checked;
            alert('Error updating testimonial status. Please try again.');
        }
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\testimonials\index.blade.php ENDPATH**/ ?>