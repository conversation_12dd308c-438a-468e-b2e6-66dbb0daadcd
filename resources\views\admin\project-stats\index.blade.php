@extends('layouts.admin')

@section('title', 'Project Stats Management')

@section('content')
<style>
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .stat-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 25px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: move;
    }

    .stat-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .stat-icon {
        font-size: 48px;
        margin-bottom: 15px;
        color: #667eea;
    }

    .stat-count {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .stat-title {
        font-size: 16px;
        font-weight: 600;
        color: #666;
        margin-bottom: 15px;
    }

    .stat-controls {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-bottom: 15px;
    }

    .count-btn {
        background: #667eea;
        color: white;
        border: none;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        transition: background 0.3s ease;
    }

    .count-btn:hover {
        background: #5a67d8;
    }

    .stat-meta {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-bottom: 15px;
        font-size: 12px;
    }

    .badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 500;
    }

    .badge-active {
        background-color: #d4edda;
        color: #155724;
    }

    .badge-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }

    .stat-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 20px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #28a745;
    }

    input:checked + .slider:before {
        transform: translateX(20px);
    }
</style>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Project Statistics ({{ $stats->count() }})</h3>
            <a href="{{ route('admin.project-stats.create') }}" class="btn btn-success">Add New Stat</a>
        </div>
    </div>
    <div class="card-body">
        @if($stats->count() > 0)
            <p style="color: #666; margin-bottom: 20px;">
                <strong>Tip:</strong> Use +/- buttons to quickly adjust counts. Drag and drop to reorder stats.
            </p>
            
            <div id="sortable-stats" class="stats-grid">
                @foreach($stats as $stat)
                <div class="stat-card" data-id="{{ $stat->id }}">
                    <div class="stat-icon">{{ $stat->icon ?? '📊' }}</div>
                    
                    <div class="stat-controls">
                        <button class="count-btn" onclick="decrementCount({{ $stat->id }})">-</button>
                        <div class="stat-count" id="count-{{ $stat->id }}">{{ $stat->count }}</div>
                        <button class="count-btn" onclick="incrementCount({{ $stat->id }})">+</button>
                    </div>
                    
                    <div class="stat-title">{{ $stat->title }}</div>
                    
                    <div class="stat-meta">
                        <span>Order: {{ $stat->order }}</span>
                        <span class="badge {{ $stat->is_active ? 'badge-active' : 'badge-inactive' }}">
                            {{ $stat->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    
                    <div class="stat-actions">
                        <label class="toggle-switch" title="Toggle Active">
                            <input type="checkbox" {{ $stat->is_active ? 'checked' : '' }} 
                                   onchange="toggleStat({{ $stat->id }}, this)">
                            <span class="slider"></span>
                        </label>
                        
                        <div style="display: flex; gap: 5px;">
                            <a href="{{ route('admin.project-stats.edit', $stat) }}" class="btn btn-sm">Edit</a>
                            <form method="POST" action="{{ route('admin.project-stats.destroy', $stat) }}" 
                                  style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div style="text-align: center; padding: 40px 20px;">
                <div style="font-size: 48px; color: #667eea; margin-bottom: 20px;">📊</div>
                <h4 style="color: #333; margin-bottom: 15px;">No Project Stats Found</h4>
                <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                    Create project statistics to showcase your achievements and project counts.
                </p>
                <a href="{{ route('admin.project-stats.create') }}" class="btn">Add First Stat</a>
            </div>
        @endif
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    $("#sortable-stats").sortable({
        update: function(event, ui) {
            var order = $(this).sortable('toArray', {attribute: 'data-id'});
            
            $.ajax({
                url: '{{ route("admin.project-stats.order") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    items: order
                },
                success: function(response) {
                    if (response.success) {
                        // Update order numbers in the UI
                        $('#sortable-stats .stat-card').each(function(index) {
                            $(this).find('.stat-meta span:first').text('Order: ' + (index + 1));
                        });
                    }
                },
                error: function() {
                    alert('Error updating order. Please refresh the page.');
                }
            });
        }
    });
});

function toggleStat(id, checkbox) {
    $.ajax({
        url: '/admin/project-stats/' + id + '/toggle',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                // Update badge
                const badge = checkbox.closest('.stat-card').querySelector('.badge-active, .badge-inactive');
                if (response.is_active) {
                    badge.className = 'badge badge-active';
                    badge.textContent = 'Active';
                } else {
                    badge.className = 'badge badge-inactive';
                    badge.textContent = 'Inactive';
                }
            }
        },
        error: function() {
            checkbox.checked = !checkbox.checked;
            alert('Error updating stat status. Please try again.');
        }
    });
}

function incrementCount(id) {
    $.ajax({
        url: '/admin/project-stats/' + id + '/increment',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                document.getElementById('count-' + id).textContent = response.count;
            }
        },
        error: function() {
            alert('Error updating count. Please try again.');
        }
    });
}

function decrementCount(id) {
    $.ajax({
        url: '/admin/project-stats/' + id + '/decrement',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                document.getElementById('count-' + id).textContent = response.count;
            }
        },
        error: function() {
            alert('Error updating count. Please try again.');
        }
    });
}
</script>
@endsection
