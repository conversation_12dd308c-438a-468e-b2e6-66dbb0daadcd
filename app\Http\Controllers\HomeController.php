<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use App\Models\NavbarItem;
use App\Models\Hero;
use App\Models\Project;
use App\Models\Service;
use App\Models\ProjectStat;
use App\Models\BlogSetting;
use App\Models\Testimonial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class HomeController extends Controller
{
    public function index()
    {
        // Get all settings
        $settings = Setting::pluck('value', 'key')->toArray();
        
        // Get active navbar items
        $navbarItems = NavbarItem::where('is_active', true)
            ->orderBy('order')
            ->get();
        
        // Get active hero section
        $hero = Hero::where('is_active', true)->first();
        
        // Get active projects (limited by homepage count setting)
        $projectsCount = $settings['projects_homepage_count'] ?? 6;
        $projects = Project::where('is_active', true)
            ->orderBy('order')
            ->limit($projectsCount)
            ->get();
        
        // Get active services (limited by homepage count setting)
        $servicesCount = $settings['services_homepage_count'] ?? 6;
        $services = Service::where('is_active', true)
            ->orderBy('order')
            ->limit($servicesCount)
            ->get();
        
        // Get active project stats
        $projectStats = ProjectStat::where('is_active', true)
            ->orderBy('order')
            ->get();
        
        // Get blog posts if enabled
        $blogPosts = [];
        $blogSettings = BlogSetting::first();
        if ($blogSettings && $blogSettings->is_active && $blogSettings->api_key) {
            try {
                $blogPosts = $this->fetchBlogPosts($blogSettings);
            } catch (\Exception $e) {
                // Silently fail for frontend
                $blogPosts = [];
            }
        }
        
        // Get active testimonials
        $testimonials = Testimonial::where('is_active', true)
            ->orderBy('order')
            ->get();
        
        return view('welcome', compact(
            'settings',
            'navbarItems',
            'hero',
            'projects',
            'services',
            'projectStats',
            'blogPosts',
            'testimonials'
        ));
    }

    private function fetchBlogPosts($settings)
    {
        $cacheKey = 'blog_posts_frontend_' . md5($settings->api_key . $settings->api_url);
        
        return cache()->remember($cacheKey, now()->addMinutes($settings->cache_duration), function () use ($settings) {
            $response = Http::timeout(10)->get($settings->api_url, [
                'apiKey' => $settings->api_key,
                'pageSize' => min($settings->posts_count, 6), // Limit to 6 for homepage
                'category' => 'technology',
                'language' => 'en',
                'sortBy' => 'publishedAt'
            ]);

            if (!$response->successful()) {
                return [];
            }

            $data = $response->json();
            
            if (!isset($data['articles']) || !is_array($data['articles'])) {
                return [];
            }

            return collect($data['articles'])->map(function ($article) {
                return [
                    'title' => $article['title'] ?? 'No Title',
                    'description' => $article['description'] ?? '',
                    'url' => $article['url'] ?? '#',
                    'image' => $article['urlToImage'] ?? null,
                    'published_at' => $article['publishedAt'] ?? null,
                    'source' => $article['source']['name'] ?? 'Unknown',
                ];
            })->filter(function ($article) {
                return !empty($article['title']) && !empty($article['url']);
            })->take(6)->toArray();
        });
    }
}
