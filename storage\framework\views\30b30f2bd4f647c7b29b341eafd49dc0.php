<?php $__env->startSection('title', 'Edit Navigation Item'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3>Edit Navigation Item: <?php echo e($navbar->title); ?></h3>
    </div>
    <div class="card-body">
        <form method="POST" action="<?php echo e(route('admin.navbar.update', $navbar)); ?>">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div class="form-group">
                <label for="title">Title *</label>
                <input type="text" id="title" name="title" class="form-control" 
                       value="<?php echo e(old('title', $navbar->title)); ?>" required>
                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            
            <div class="form-group">
                <label for="url">URL *</label>
                <input type="text" id="url" name="url" class="form-control" 
                       value="<?php echo e(old('url', $navbar->url)); ?>" required>
                <small style="color: #666;">Enter the URL path (e.g., /about, /contact, or external URL)</small>
                <?php $__errorArgs = ['url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            
            <div class="form-group">
                <label for="order">Order *</label>
                <input type="number" id="order" name="order" class="form-control" 
                       value="<?php echo e(old('order', $navbar->order)); ?>" min="1" required>
                <small style="color: #666;">Lower numbers appear first in the navigation</small>
                <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            
            <div class="form-group">
                <label for="color">Custom Color (Optional)</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <input type="color" id="color" name="color" value="<?php echo e(old('color', $navbar->color ?? '#667eea')); ?>">
                    <input type="text" class="form-control" style="flex: 1;" 
                           value="<?php echo e(old('color', $navbar->color ?? '#667eea')); ?>" readonly id="color-text">
                </div>
                <small style="color: #666;">Choose a custom color for this navigation item</small>
                <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            
            <div class="form-group">
                <label style="display: flex; align-items: center; gap: 10px;">
                    <input type="checkbox" name="auto_generated" value="1" 
                           <?php echo e(old('auto_generated', $navbar->auto_generated) ? 'checked' : ''); ?>>
                    <span>Auto-generated Page</span>
                </label>
                <small style="color: #666;">Check this if this item should generate a standalone page automatically</small>
            </div>
            
            <div class="form-group">
                <label style="display: flex; align-items: center; gap: 10px;">
                    <input type="checkbox" name="is_active" value="1" 
                           <?php echo e(old('is_active', $navbar->is_active) ? 'checked' : ''); ?>>
                    <span>Active</span>
                </label>
                <small style="color: #666;">Uncheck to hide this item from the navigation</small>
            </div>
            
            <div style="display: flex; gap: 10px;">
                <button type="submit" class="btn">Update Navigation Item</button>
                <a href="<?php echo e(route('admin.navbar.index')); ?>" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('color').addEventListener('change', function() {
    document.getElementById('color-text').value = this.value;
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\navbar\edit.blade.php ENDPATH**/ ?>