<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProjectStat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProjectStatsController extends Controller
{
    public function index()
    {
        $stats = ProjectStat::orderBy('order')->get();
        return view('admin.project-stats.index', compact('stats'));
    }

    public function create()
    {
        return view('admin.project-stats.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'count' => 'required|integer|min:0',
            'icon' => 'nullable|string|max:10',
            'order' => 'required|integer|min:1',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        ProjectStat::create([
            'title' => $request->title,
            'count' => $request->count,
            'icon' => $request->icon,
            'order' => $request->order,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('admin.project-stats.index')->with('success', 'Project stat created successfully!');
    }

    public function edit(ProjectStat $projectStat)
    {
        return view('admin.project-stats.edit', compact('projectStat'));
    }

    public function update(Request $request, ProjectStat $projectStat)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'count' => 'required|integer|min:0',
            'icon' => 'nullable|string|max:10',
            'order' => 'required|integer|min:1',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $projectStat->update([
            'title' => $request->title,
            'count' => $request->count,
            'icon' => $request->icon,
            'order' => $request->order,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('admin.project-stats.index')->with('success', 'Project stat updated successfully!');
    }

    public function destroy(ProjectStat $projectStat)
    {
        $projectStat->delete();
        return redirect()->route('admin.project-stats.index')->with('success', 'Project stat deleted successfully!');
    }

    public function updateOrder(Request $request)
    {
        $items = $request->input('items', []);
        
        foreach ($items as $index => $id) {
            ProjectStat::where('id', $id)->update(['order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }

    public function toggle(ProjectStat $projectStat)
    {
        $projectStat->update(['is_active' => !$projectStat->is_active]);
        
        return response()->json([
            'success' => true,
            'is_active' => $projectStat->is_active
        ]);
    }

    public function incrementCount(ProjectStat $projectStat)
    {
        $projectStat->increment('count');
        
        return response()->json([
            'success' => true,
            'count' => $projectStat->count
        ]);
    }

    public function decrementCount(ProjectStat $projectStat)
    {
        if ($projectStat->count > 0) {
            $projectStat->decrement('count');
        }
        
        return response()->json([
            'success' => true,
            'count' => $projectStat->count
        ]);
    }
}
