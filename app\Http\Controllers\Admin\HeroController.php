<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HeroSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class HeroController extends Controller
{
    public function index()
    {
        $hero = HeroSection::where('is_active', true)->first();
        return view('admin.hero.index', compact('hero'));
    }

    public function create()
    {
        return view('admin.hero.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cta_primary_text' => 'nullable|string|max:100',
            'cta_primary_url' => 'nullable|string|max:255',
            'cta_secondary_text' => 'nullable|string|max:100',
            'cta_secondary_url' => 'nullable|string|max:255',
            'show_galaxy_animation' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Deactivate existing hero sections
        HeroSection::where('is_active', true)->update(['is_active' => false]);

        $data = [
            'name' => $request->name,
            'title' => $request->title,
            'description' => $request->description,
            'cta_primary_text' => $request->cta_primary_text,
            'cta_primary_url' => $request->cta_primary_url,
            'cta_secondary_text' => $request->cta_secondary_text,
            'cta_secondary_url' => $request->cta_secondary_url,
            'show_galaxy_animation' => $request->boolean('show_galaxy_animation'),
            'is_active' => true,
        ];

        // Handle photo upload
        if ($request->hasFile('photo')) {
            $photo = $request->file('photo');
            $filename = time() . '_hero_' . $photo->getClientOriginalName();
            $photo->move(public_path('images'), $filename);
            $data['photo'] = 'images/' . $filename;
        }

        HeroSection::create($data);

        return redirect()->route('admin.hero.index')->with('success', 'Hero section created successfully!');
    }

    public function edit(HeroSection $hero)
    {
        return view('admin.hero.edit', compact('hero'));
    }

    public function update(Request $request, HeroSection $hero)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cta_primary_text' => 'nullable|string|max:100',
            'cta_primary_url' => 'nullable|string|max:255',
            'cta_secondary_text' => 'nullable|string|max:100',
            'cta_secondary_url' => 'nullable|string|max:255',
            'show_galaxy_animation' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = [
            'name' => $request->name,
            'title' => $request->title,
            'description' => $request->description,
            'cta_primary_text' => $request->cta_primary_text,
            'cta_primary_url' => $request->cta_primary_url,
            'cta_secondary_text' => $request->cta_secondary_text,
            'cta_secondary_url' => $request->cta_secondary_url,
            'show_galaxy_animation' => $request->boolean('show_galaxy_animation'),
        ];

        // Handle photo upload
        if ($request->hasFile('photo')) {
            // Delete old photo if exists
            if ($hero->photo && file_exists(public_path($hero->photo))) {
                unlink(public_path($hero->photo));
            }

            $photo = $request->file('photo');
            $filename = time() . '_hero_' . $photo->getClientOriginalName();
            $photo->move(public_path('images'), $filename);
            $data['photo'] = 'images/' . $filename;
        }

        $hero->update($data);

        return redirect()->route('admin.hero.index')->with('success', 'Hero section updated successfully!');
    }

    public function destroy(HeroSection $hero)
    {
        // Delete photo if exists
        if ($hero->photo && file_exists(public_path($hero->photo))) {
            unlink(public_path($hero->photo));
        }

        $hero->delete();
        return redirect()->route('admin.hero.index')->with('success', 'Hero section deleted successfully!');
    }

    public function activate(HeroSection $hero)
    {
        // Deactivate all other hero sections
        HeroSection::where('is_active', true)->update(['is_active' => false]);
        
        // Activate this hero section
        $hero->update(['is_active' => true]);

        return response()->json(['success' => true]);
    }
}
