<?php

namespace App\Helpers;

use App\Models\SiteSetting;

class SettingsHelper
{
    /**
     * Get a setting value with fallback
     */
    public static function get($key, $default = null)
    {
        return SiteSetting::get($key, $default);
    }

    /**
     * Get all color settings
     */
    public static function getColors()
    {
        return [
            'primary' => self::get('primary_color', '#3b82f6'),
            'secondary' => self::get('secondary_color', '#64748b'),
            'background' => self::get('background_color', '#ffffff'),
            'text' => self::get('text_color', '#1f2937'),
        ];
    }

    /**
     * Get section visibility settings
     */
    public static function getSectionVisibility()
    {
        return [
            'navbar' => self::get('show_navbar', '1') === '1',
            'hero' => self::get('show_hero', '1') === '1',
            'services' => self::get('show_services', '1') === '1',
            'projects' => self::get('show_projects', '1') === '1',
            'stats' => self::get('show_stats', '1') === '1',
            'blog' => self::get('show_blog', '1') === '1',
            'testimonials' => self::get('show_testimonials', '1') === '1',
            'footer' => self::get('show_footer', '1') === '1',
        ];
    }

    /**
     * Get content count settings
     */
    public static function getContentCounts()
    {
        return [
            'homepage_projects' => (int) self::get('homepage_projects_count', '6'),
            'homepage_services' => (int) self::get('homepage_services_count', '6'),
            'homepage_blog' => (int) self::get('homepage_blog_count', '6'),
        ];
    }

    /**
     * Get section titles and descriptions
     */
    public static function getSectionContent($section)
    {
        $defaults = [
            'blog' => [
                'title' => 'Latest Tech News',
                'description' => 'Stay updated with the latest technology trends and news'
            ],
            'projects' => [
                'title' => 'My Projects',
                'description' => 'Here are some of my recent projects'
            ],
            'services' => [
                'title' => 'My Services',
                'description' => 'What I can do for you'
            ],
            'testimonials' => [
                'title' => 'What Clients Say',
                'description' => 'Feedback from satisfied clients'
            ],
            'footer' => [
                'description' => 'Professional 2D/3D Artist and Web Developer creating amazing digital experiences.'
            ]
        ];

        $sectionDefaults = $defaults[$section] ?? [];
        
        return [
            'title' => self::get($section . '_title', $sectionDefaults['title'] ?? ''),
            'description' => self::get($section . '_description', $sectionDefaults['description'] ?? ''),
        ];
    }

    /**
     * Check if a section should be shown
     */
    public static function shouldShowSection($section)
    {
        $visibility = self::getSectionVisibility();
        return $visibility[$section] ?? true;
    }

    /**
     * Generate CSS variables for colors
     */
    public static function getCssVariables()
    {
        $colors = self::getColors();
        
        return [
            '--primary-color' => $colors['primary'],
            '--secondary-color' => $colors['secondary'],
            '--background-color' => $colors['background'],
            '--text-color' => $colors['text'],
        ];
    }
}
