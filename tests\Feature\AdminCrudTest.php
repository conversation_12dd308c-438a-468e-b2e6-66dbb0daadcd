<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Project;
use App\Models\Service;
use App\Models\NavbarItem;
use App\Models\Testimonial;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AdminCrudTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);
        
        Storage::fake('public');
    }

    public function test_admin_can_view_projects_index()
    {
        $this->actingAs($this->admin);
        
        Project::factory()->count(3)->create();
        
        $response = $this->get('/admin/projects');
        $response->assertStatus(200);
        $response->assertViewIs('admin.projects.index');
    }

    public function test_admin_can_create_project()
    {
        $this->actingAs($this->admin);
        
        $image = UploadedFile::fake()->image('project.jpg');
        
        $response = $this->post('/admin/projects', [
            'title' => 'Test Project',
            'description' => 'Test project description',
            'url' => 'https://example.com',
            'technologies' => 'Laravel, Vue.js',
            'image' => $image,
            'is_active' => true,
            'is_featured' => true,
        ]);
        
        $response->assertRedirect('/admin/projects');
        $this->assertDatabaseHas('projects', [
            'title' => 'Test Project',
            'description' => 'Test project description',
        ]);
        
        Storage::disk('public')->assertExists('images/projects/' . $image->hashName());
    }

    public function test_admin_can_update_project()
    {
        $this->actingAs($this->admin);
        
        $project = Project::factory()->create([
            'title' => 'Original Title',
        ]);
        
        $response = $this->put("/admin/projects/{$project->id}", [
            'title' => 'Updated Title',
            'description' => $project->description,
            'url' => $project->url,
            'technologies' => $project->technologies,
            'is_active' => $project->is_active,
            'is_featured' => $project->is_featured,
        ]);
        
        $response->assertRedirect('/admin/projects');
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'title' => 'Updated Title',
        ]);
    }

    public function test_admin_can_delete_project()
    {
        $this->actingAs($this->admin);
        
        $project = Project::factory()->create();
        
        $response = $this->delete("/admin/projects/{$project->id}");
        $response->assertRedirect('/admin/projects');
        $this->assertDatabaseMissing('projects', [
            'id' => $project->id,
        ]);
    }

    public function test_admin_can_toggle_project_status()
    {
        $this->actingAs($this->admin);
        
        $project = Project::factory()->create(['is_active' => true]);
        
        $response = $this->post("/admin/projects/{$project->id}/toggle");
        $response->assertJson(['success' => true]);
        
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'is_active' => false,
        ]);
    }

    public function test_admin_can_update_project_order()
    {
        $this->actingAs($this->admin);
        
        $project1 = Project::factory()->create(['order' => 1]);
        $project2 = Project::factory()->create(['order' => 2]);
        
        $response = $this->post('/admin/projects/order', [
            'items' => [
                ['id' => $project2->id, 'order' => 1],
                ['id' => $project1->id, 'order' => 2],
            ]
        ]);
        
        $response->assertJson(['success' => true]);
        
        $this->assertDatabaseHas('projects', [
            'id' => $project2->id,
            'order' => 1,
        ]);
        
        $this->assertDatabaseHas('projects', [
            'id' => $project1->id,
            'order' => 2,
        ]);
    }

    public function test_admin_can_manage_services()
    {
        $this->actingAs($this->admin);
        
        // Test create
        $icon = UploadedFile::fake()->image('service-icon.png');
        
        $response = $this->post('/admin/services', [
            'title' => 'Web Development',
            'description' => 'Professional web development services',
            'features' => "Custom websites\nResponsive design\nSEO optimization",
            'icon' => $icon,
            'is_active' => true,
        ]);
        
        $response->assertRedirect('/admin/services');
        $this->assertDatabaseHas('services', [
            'title' => 'Web Development',
        ]);
    }

    public function test_admin_can_manage_navbar_items()
    {
        $this->actingAs($this->admin);
        
        $response = $this->post('/admin/navbar', [
            'title' => 'About',
            'url' => '/about',
            'is_active' => true,
        ]);
        
        $response->assertRedirect('/admin/navbar');
        $this->assertDatabaseHas('navbar_items', [
            'title' => 'About',
            'url' => '/about',
        ]);
    }

    public function test_admin_can_manage_testimonials()
    {
        $this->actingAs($this->admin);
        
        $image = UploadedFile::fake()->image('testimonial.jpg');
        
        $response = $this->post('/admin/testimonials', [
            'name' => 'John Doe',
            'role' => 'CEO',
            'company' => 'Example Corp',
            'quote' => 'Excellent work!',
            'rating' => 5,
            'image' => $image,
            'is_active' => true,
        ]);
        
        $response->assertRedirect('/admin/testimonials');
        $this->assertDatabaseHas('testimonials', [
            'name' => 'John Doe',
            'quote' => 'Excellent work!',
        ]);
    }

    public function test_validation_errors_are_handled()
    {
        $this->actingAs($this->admin);
        
        $response = $this->post('/admin/projects', [
            'title' => '', // Required field
            'description' => '',
        ]);
        
        $response->assertSessionHasErrors(['title', 'description']);
    }

    public function test_unauthorized_user_cannot_access_admin()
    {
        $user = User::factory()->create([
            'role' => 'user', // Not admin or editor
            'email_verified_at' => now(),
        ]);
        
        $this->actingAs($user);
        
        $response = $this->get('/admin/projects');
        $response->assertStatus(403);
    }

    public function test_editor_can_access_admin_crud()
    {
        $editor = User::factory()->create([
            'role' => 'editor',
            'email_verified_at' => now(),
        ]);
        
        $this->actingAs($editor);
        
        $response = $this->get('/admin/projects');
        $response->assertStatus(200);
    }

    public function test_file_upload_validation()
    {
        $this->actingAs($this->admin);
        
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1000);
        
        $response = $this->post('/admin/projects', [
            'title' => 'Test Project',
            'description' => 'Test description',
            'image' => $invalidFile, // Should be image
            'is_active' => true,
        ]);
        
        $response->assertSessionHasErrors(['image']);
    }

    public function test_csrf_protection()
    {
        $this->actingAs($this->admin);
        
        // Disable CSRF for this test
        $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);
        
        $response = $this->post('/admin/projects', [
            'title' => 'Test Project',
            'description' => 'Test description',
        ]);
        
        // Should work without CSRF token when middleware is disabled
        $response->assertRedirect();
    }
}
