@extends('layouts.auth')

@section('title', 'Register')

@section('content')
<div class="auth-header">
    <h1>Create Account</h1>
    <p>Sign up for a new account</p>
</div>

@if ($errors->any())
    <div class="alert alert-danger">
        @foreach ($errors->all() as $error)
            <div>{{ $error }}</div>
        @endforeach
    </div>
@endif

<form method="POST" action="{{ route('register') }}">
    @csrf
    
    <div class="form-group">
        <label for="name">Full Name</label>
        <input type="text" id="name" name="name" class="form-control" value="{{ old('name') }}" required autofocus>
    </div>
    
    <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" id="email" name="email" class="form-control" value="{{ old('email') }}" required>
    </div>
    
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" class="form-control" required>
    </div>
    
    <div class="form-group">
        <label for="password_confirmation">Confirm Password</label>
        <input type="password" id="password_confirmation" name="password_confirmation" class="form-control" required>
    </div>
    
    <button type="submit" class="btn">Create Account</button>
</form>

<div class="auth-links">
    <a href="{{ route('login') }}">Already have an account? Sign in</a>
</div>
@endsection
