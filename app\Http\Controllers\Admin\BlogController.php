<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class BlogController extends Controller
{
    public function index()
    {
        $settings = BlogSetting::first();
        $posts = [];
        $error = null;

        if ($settings && $settings->api_key && $settings->is_active) {
            try {
                $posts = $this->fetchBlogPosts($settings);
            } catch (\Exception $e) {
                $error = 'Failed to fetch blog posts: ' . $e->getMessage();
            }
        }

        return view('admin.blog.index', compact('settings', 'posts', 'error'));
    }

    public function settings()
    {
        $settings = BlogSetting::first();
        return view('admin.blog.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'api_key' => 'required|string|max:255',
            'api_url' => 'required|url|max:500',
            'posts_count' => 'required|integer|min:1|max:20',
            'cache_duration' => 'required|integer|min:5|max:1440',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $settings = BlogSetting::first();
        
        if (!$settings) {
            $settings = new BlogSetting();
        }

        $settings->update([
            'api_key' => $request->api_key,
            'api_url' => $request->api_url,
            'posts_count' => $request->posts_count,
            'cache_duration' => $request->cache_duration,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('admin.blog.index')->with('success', 'Blog settings updated successfully!');
    }

    public function testConnection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'api_key' => 'required|string',
            'api_url' => 'required|url',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => 'Invalid API credentials']);
        }

        try {
            $response = Http::timeout(10)->get($request->api_url, [
                'apiKey' => $request->api_key,
                'pageSize' => 1,
                'category' => 'technology'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['articles']) && is_array($data['articles'])) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Connection successful! Found ' . $data['totalResults'] . ' articles.'
                    ]);
                }
            }

            return response()->json([
                'success' => false,
                'message' => 'API responded but data format is unexpected'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ]);
        }
    }

    public function refreshPosts()
    {
        $settings = BlogSetting::first();
        
        if (!$settings || !$settings->api_key || !$settings->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Blog API is not configured or inactive'
            ]);
        }

        try {
            $posts = $this->fetchBlogPosts($settings);
            
            return response()->json([
                'success' => true,
                'message' => 'Posts refreshed successfully!',
                'posts_count' => count($posts)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh posts: ' . $e->getMessage()
            ]);
        }
    }

    private function fetchBlogPosts($settings)
    {
        $cacheKey = 'blog_posts_' . md5($settings->api_key . $settings->api_url);
        
        return cache()->remember($cacheKey, now()->addMinutes($settings->cache_duration), function () use ($settings) {
            $response = Http::timeout(15)->get($settings->api_url, [
                'apiKey' => $settings->api_key,
                'pageSize' => $settings->posts_count,
                'category' => 'technology',
                'language' => 'en',
                'sortBy' => 'publishedAt'
            ]);

            if (!$response->successful()) {
                throw new \Exception('API request failed with status: ' . $response->status());
            }

            $data = $response->json();
            
            if (!isset($data['articles']) || !is_array($data['articles'])) {
                throw new \Exception('Invalid API response format');
            }

            return collect($data['articles'])->map(function ($article) {
                return [
                    'title' => $article['title'] ?? 'No Title',
                    'description' => $article['description'] ?? '',
                    'url' => $article['url'] ?? '#',
                    'image' => $article['urlToImage'] ?? null,
                    'published_at' => $article['publishedAt'] ?? null,
                    'source' => $article['source']['name'] ?? 'Unknown',
                ];
            })->filter(function ($article) {
                return !empty($article['title']) && !empty($article['url']);
            })->take($settings->posts_count)->toArray();
        });
    }
}
