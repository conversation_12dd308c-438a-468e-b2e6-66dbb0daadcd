<style>
:root {
    --primary-color: <?php echo e($site_colors['primary']); ?>;
    --secondary-color: <?php echo e($site_colors['secondary']); ?>;
    --background-color: <?php echo e($site_colors['background']); ?>;
    --text-color: <?php echo e($site_colors['text']); ?>;
}

/* Apply dynamic colors to common elements */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary-color) 85%, black);
    border-color: color-mix(in srgb, var(--primary-color) 85%, black);
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.navbar-brand,
.nav-link {
    color: var(--text-color);
}

.hero-section {
    background-color: var(--background-color);
    color: var(--text-color);
}

.section-title {
    color: var(--primary-color);
}

.card {
    background-color: var(--background-color);
    color: var(--text-color);
}

/* Links */
a {
    color: var(--primary-color);
}

a:hover {
    color: color-mix(in srgb, var(--primary-color) 85%, black);
}

/* Buttons */
.btn {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn:hover {
    background-color: color-mix(in srgb, var(--primary-color) 85%, black);
    border-color: color-mix(in srgb, var(--primary-color) 85%, black);
}

.btn-outline {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Form elements */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem color-mix(in srgb, var(--primary-color) 25%, transparent);
}

/* Project and service cards */
.project-card:hover,
.service-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px color-mix(in srgb, var(--primary-color) 20%, transparent);
}

/* Stats counters */
.stat-number {
    color: var(--primary-color);
}

/* Testimonial cards */
.testimonial-card {
    background-color: var(--background-color);
    color: var(--text-color);
}

/* Footer */
.footer {
    background-color: color-mix(in srgb, var(--text-color) 95%, white);
    color: var(--background-color);
}

.footer a {
    color: var(--primary-color);
}

.footer a:hover {
    color: color-mix(in srgb, var(--primary-color) 85%, white);
}
</style>
<?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\components\dynamic-styles.blade.php ENDPATH**/ ?>