<?php $__env->startSection('title', 'Projects Management'); ?>

<?php $__env->startSection('content'); ?>
<style>
    .projects-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .projects-grid.ui-sortable .project-card {
        cursor: move;
    }

    .project-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: move;
    }

    .project-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .project-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
    }

    .project-content {
        padding: 15px;
    }

    .project-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
    }

    .project-description {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 15px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .project-meta {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        font-size: 12px;
    }

    .badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 500;
    }

    .badge-featured {
        background-color: #fff3cd;
        color: #856404;
    }

    .badge-active {
        background-color: #d4edda;
        color: #155724;
    }

    .badge-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }

    .project-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 20px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #28a745;
    }

    input:checked + .slider:before {
        transform: translateX(20px);
    }

    .star-toggle {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        color: #ddd;
        transition: color 0.3s ease;
    }

    .star-toggle.featured {
        color: #ffc107;
    }
</style>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Projects (<?php echo e($projects->count()); ?>)</h3>
            <a href="<?php echo e(route('admin.projects.create')); ?>" class="btn btn-success">Add New Project</a>
        </div>
    </div>
    <div class="card-body">
        <?php if($projects->count() > 0): ?>
            <p style="color: #666; margin-bottom: 20px;">
                <strong>Tip:</strong> Drag and drop projects to reorder them. Use toggles to control visibility and featured status.
            </p>
            
            <div id="sortable-projects" class="projects-grid">
                <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="project-card" data-id="<?php echo e($project->id); ?>">
                    <?php if($project->image): ?>
                        <img src="<?php echo e(asset($project->image)); ?>" alt="<?php echo e($project->title); ?>" class="project-image">
                    <?php else: ?>
                        <div class="project-image">
                            <span>No Image</span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="project-content">
                        <div class="project-title"><?php echo e($project->title); ?></div>
                        <div class="project-description"><?php echo e($project->description); ?></div>
                        
                        <div class="project-meta">
                            <span>Order: <?php echo e($project->order); ?></span>
                            <?php if($project->is_featured): ?>
                                <span class="badge badge-featured">Featured</span>
                            <?php endif; ?>
                            <span class="badge <?php echo e($project->is_active ? 'badge-active' : 'badge-inactive'); ?>">
                                <?php echo e($project->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>
                        
                        <div class="project-actions">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <button class="star-toggle <?php echo e($project->is_featured ? 'featured' : ''); ?>" 
                                        onclick="toggleFeatured(<?php echo e($project->id); ?>, this)" title="Toggle Featured">
                                    ★
                                </button>
                                
                                <label class="toggle-switch" title="Toggle Active">
                                    <input type="checkbox" <?php echo e($project->is_active ? 'checked' : ''); ?> 
                                           onchange="toggleProject(<?php echo e($project->id); ?>, this)">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div style="display: flex; gap: 5px;">
                                <a href="<?php echo e(route('admin.projects.edit', $project)); ?>" class="btn btn-sm">Edit</a>
                                <form method="POST" action="<?php echo e(route('admin.projects.destroy', $project)); ?>" 
                                      style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: 40px 20px;">
                <div style="font-size: 48px; color: #667eea; margin-bottom: 20px;">📁</div>
                <h4 style="color: #333; margin-bottom: 15px;">No Projects Found</h4>
                <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                    Start building your portfolio by adding your first project.
                </p>
                <a href="<?php echo e(route('admin.projects.create')); ?>" class="btn">Add First Project</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    $("#sortable-projects").sortable({
        update: function(event, ui) {
            var order = $(this).sortable('toArray', {attribute: 'data-id'});
            
            $.ajax({
                url: '<?php echo e(route("admin.projects.order")); ?>',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    items: order
                },
                success: function(response) {
                    if (response.success) {
                        // Update order numbers in the UI
                        $('#sortable-projects .project-card').each(function(index) {
                            $(this).find('.project-meta span:first').text('Order: ' + (index + 1));
                        });
                    }
                },
                error: function() {
                    alert('Error updating order. Please refresh the page.');
                }
            });
        }
    });
});

function toggleProject(id, checkbox) {
    $.ajax({
        url: '/admin/projects/' + id + '/toggle',
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                // Update badge
                const badge = checkbox.closest('.project-card').querySelector('.badge-active, .badge-inactive');
                if (response.is_active) {
                    badge.className = 'badge badge-active';
                    badge.textContent = 'Active';
                } else {
                    badge.className = 'badge badge-inactive';
                    badge.textContent = 'Inactive';
                }
            }
        },
        error: function() {
            checkbox.checked = !checkbox.checked;
            alert('Error updating project status. Please try again.');
        }
    });
}

function toggleFeatured(id, button) {
    $.ajax({
        url: '/admin/projects/' + id + '/toggle-featured',
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                if (response.is_featured) {
                    button.classList.add('featured');
                    // Add featured badge if not exists
                    const meta = button.closest('.project-card').querySelector('.project-meta');
                    if (!meta.querySelector('.badge-featured')) {
                        const badge = document.createElement('span');
                        badge.className = 'badge badge-featured';
                        badge.textContent = 'Featured';
                        meta.insertBefore(badge, meta.children[1]);
                    }
                } else {
                    button.classList.remove('featured');
                    // Remove featured badge
                    const badge = button.closest('.project-card').querySelector('.badge-featured');
                    if (badge) badge.remove();
                }
            }
        },
        error: function() {
            alert('Error updating featured status. Please try again.');
        }
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\projects\index.blade.php ENDPATH**/ ?>