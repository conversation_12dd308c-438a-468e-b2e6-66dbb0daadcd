/* Portfolio Animations and Interactive Elements */

/* Galaxy Star Animation Background */
.galaxy-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.star {
    position: absolute;
    background: white;
    border-radius: 50%;
    opacity: 0;
    animation: twinkle 3s infinite;
}

.star.small {
    width: 1px;
    height: 1px;
}

.star.medium {
    width: 2px;
    height: 2px;
}

.star.large {
    width: 3px;
    height: 3px;
}

/* Star twinkle animation */
@keyframes twinkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Shooting star animation */
.shooting-star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: linear-gradient(45deg, white, transparent);
    border-radius: 50%;
    animation: shoot 4s linear infinite;
}

@keyframes shoot {
    0% {
        transform: translateX(-100px) translateY(100px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateX(100vw) translateY(-100px);
        opacity: 0;
    }
}

/* Galaxy particles floating animation */
.galaxy-particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

/* Hero content animations */
.hero-content > * {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.hero-content h1 {
    animation-delay: 0.2s;
}

.hero-content .subtitle {
    animation-delay: 0.4s;
}

.hero-content .description {
    animation-delay: 0.6s;
}

.hero-content .cta-buttons {
    animation-delay: 0.8s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Button hover animations */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn:active {
    transform: translateY(0);
}

/* Card hover animations */
.card {
    transition: all 0.3s ease;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
    z-index: -1;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card:hover::before {
    opacity: 0.05;
}

/* Testimonial carousel animations */
.testimonials-carousel {
    position: relative;
    overflow: hidden;
}

.testimonial-slide {
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease-in-out;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}

.testimonial-slide.active {
    opacity: 1;
    transform: translateX(0);
    position: relative;
}

.testimonial-slide.prev {
    transform: translateX(-100%);
}

.testimonial-slide.next {
    transform: translateX(100%);
}

/* Carousel navigation */
.carousel-nav {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-dot.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

.carousel-dot:hover {
    background: var(--primary-color);
    opacity: 0.7;
}

/* Carousel arrows */
.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
}

.carousel-arrow:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-50%) scale(1.1);
}

.carousel-arrow.prev {
    left: 1rem;
}

.carousel-arrow.next {
    right: 1rem;
}

/* Loading animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-left: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pulse animation for loading states */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Skeleton loading animation */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Scroll animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Stagger animation for grid items */
.grid-item {
    opacity: 0;
    transform: translateY(30px);
    animation: slideInUp 0.6s ease-out forwards;
}

.grid-item:nth-child(1) { animation-delay: 0.1s; }
.grid-item:nth-child(2) { animation-delay: 0.2s; }
.grid-item:nth-child(3) { animation-delay: 0.3s; }
.grid-item:nth-child(4) { animation-delay: 0.4s; }
.grid-item:nth-child(5) { animation-delay: 0.5s; }
.grid-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Counter animation */
.counter {
    font-variant-numeric: tabular-nums;
}

.counter.animate {
    animation: countUp 2s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Typing animation */
.typing-text {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--primary-color); }
}

/* Parallax effect */
.parallax {
    transform: translateZ(0);
    transition: transform 0.1s ease-out;
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Section reveal animation */
.section {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.section.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* Image hover effects */
.image-hover {
    overflow: hidden;
    position: relative;
}

.image-hover img {
    transition: transform 0.3s ease;
}

.image-hover:hover img {
    transform: scale(1.1);
}

.image-hover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-hover:hover::after {
    opacity: 0.2;
}

/* Text reveal animation */
.text-reveal {
    overflow: hidden;
}

.text-reveal span {
    display: inline-block;
    transform: translateY(100%);
    transition: transform 0.6s ease;
}

.text-reveal.revealed span {
    transform: translateY(0);
}

/* Floating action button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Progress bar animation */
.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    transform: translateX(-100%);
    transition: transform 1s ease;
}

.progress-fill.animate {
    transform: translateX(0);
}

/* Responsive animations */
@media (max-width: 768px) {
    .card:hover {
        transform: translateY(-4px) scale(1.01);
    }
    
    .carousel-arrow {
        width: 32px;
        height: 32px;
    }
    
    .fab {
        width: 48px;
        height: 48px;
        bottom: 1rem;
        right: 1rem;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .galaxy-background,
    .star,
    .shooting-star,
    .galaxy-particle {
        display: none;
    }
}
