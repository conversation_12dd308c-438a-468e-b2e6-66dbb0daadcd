@extends('layouts.admin')

@section('title', 'Site Settings')

@section('content')
<style>
    .settings-tabs {
        display: flex;
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 30px;
    }

    .tab-button {
        padding: 12px 24px;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .tab-button.active {
        color: #667eea;
        border-bottom-color: #667eea;
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    .color-input-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .color-preview {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        border: 2px solid #ddd;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #667eea;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .settings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }
</style>

<div class="settings-tabs">
    <button class="tab-button active" onclick="showTab('colors')">Colors</button>
    <button class="tab-button" onclick="showTab('sections')">Section Visibility</button>
    <button class="tab-button" onclick="showTab('content')">Content Settings</button>
    <button class="tab-button" onclick="showTab('titles')">Section Titles</button>
</div>

<!-- Colors Tab -->
<div id="colors" class="tab-content active">
    <div class="card">
        <div class="card-header">
            <h3>Color Customization</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.settings.colors') }}">
                @csrf
                
                <div class="settings-grid">
                    <div class="form-group">
                        <label for="primary_color">Primary Color</label>
                        <div class="color-input-group">
                            <input type="color" id="primary_color" name="primary_color" 
                                   value="{{ $settings['colors']['primary_color']->value ?? '#3b82f6' }}"
                                   onchange="updateColorPreview('primary_color')">
                            <div class="color-preview" id="primary_color_preview" 
                                 style="background-color: {{ $settings['colors']['primary_color']->value ?? '#3b82f6' }}"></div>
                            <input type="text" class="form-control" style="flex: 1;" 
                                   value="{{ $settings['colors']['primary_color']->value ?? '#3b82f6' }}" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="secondary_color">Secondary Color</label>
                        <div class="color-input-group">
                            <input type="color" id="secondary_color" name="secondary_color" 
                                   value="{{ $settings['colors']['secondary_color']->value ?? '#64748b' }}"
                                   onchange="updateColorPreview('secondary_color')">
                            <div class="color-preview" id="secondary_color_preview" 
                                 style="background-color: {{ $settings['colors']['secondary_color']->value ?? '#64748b' }}"></div>
                            <input type="text" class="form-control" style="flex: 1;" 
                                   value="{{ $settings['colors']['secondary_color']->value ?? '#64748b' }}" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="background_color">Background Color</label>
                        <div class="color-input-group">
                            <input type="color" id="background_color" name="background_color" 
                                   value="{{ $settings['colors']['background_color']->value ?? '#ffffff' }}"
                                   onchange="updateColorPreview('background_color')">
                            <div class="color-preview" id="background_color_preview" 
                                 style="background-color: {{ $settings['colors']['background_color']->value ?? '#ffffff' }}"></div>
                            <input type="text" class="form-control" style="flex: 1;" 
                                   value="{{ $settings['colors']['background_color']->value ?? '#ffffff' }}" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="text_color">Text Color</label>
                        <div class="color-input-group">
                            <input type="color" id="text_color" name="text_color" 
                                   value="{{ $settings['colors']['text_color']->value ?? '#1f2937' }}"
                                   onchange="updateColorPreview('text_color')">
                            <div class="color-preview" id="text_color_preview" 
                                 style="background-color: {{ $settings['colors']['text_color']->value ?? '#1f2937' }}"></div>
                            <input type="text" class="form-control" style="flex: 1;" 
                                   value="{{ $settings['colors']['text_color']->value ?? '#1f2937' }}" readonly>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn">Update Colors</button>
            </form>
        </div>
    </div>
</div>

<!-- Section Visibility Tab -->
<div id="sections" class="tab-content">
    <div class="card">
        <div class="card-header">
            <h3>Section Visibility</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.settings.sections') }}">
                @csrf
                
                <div class="settings-grid">
                    @php
                        $sectionLabels = [
                            'show_navbar' => 'Navigation Bar',
                            'show_hero' => 'Hero Section',
                            'show_services' => 'Services Section',
                            'show_projects' => 'Projects Section',
                            'show_stats' => 'Project Stats Section',
                            'show_blog' => 'Blog Section',
                            'show_testimonials' => 'Testimonials Section',
                            'show_footer' => 'Footer Section'
                        ];
                    @endphp

                    @foreach($sectionLabels as $key => $label)
                    <div class="form-group">
                        <label style="display: flex; align-items: center; justify-content: space-between;">
                            <span>{{ $label }}</span>
                            <label class="toggle-switch">
                                <input type="checkbox" name="{{ $key }}" 
                                       {{ ($settings['sections'][$key]->value ?? '1') == '1' ? 'checked' : '' }}>
                                <span class="slider"></span>
                            </label>
                        </label>
                    </div>
                    @endforeach
                </div>

                <button type="submit" class="btn">Update Section Visibility</button>
            </form>
        </div>
    </div>
</div>

<!-- Content Settings Tab -->
<div id="content" class="tab-content">
    <div class="card">
        <div class="card-header">
            <h3>Content Count Settings</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.settings.content') }}">
                @csrf
                
                <div class="settings-grid">
                    <div class="form-group">
                        <label for="homepage_projects_count">Homepage Projects Count</label>
                        <input type="number" id="homepage_projects_count" name="homepage_projects_count" 
                               class="form-control" min="1" max="20"
                               value="{{ $settings['content']['homepage_projects_count']->value ?? '6' }}">
                    </div>

                    <div class="form-group">
                        <label for="homepage_services_count">Homepage Services Count</label>
                        <input type="number" id="homepage_services_count" name="homepage_services_count" 
                               class="form-control" min="1" max="20"
                               value="{{ $settings['content']['homepage_services_count']->value ?? '6' }}">
                    </div>

                    <div class="form-group">
                        <label for="homepage_blog_count">Homepage Blog Posts Count</label>
                        <input type="number" id="homepage_blog_count" name="homepage_blog_count" 
                               class="form-control" min="1" max="20"
                               value="{{ $settings['content']['homepage_blog_count']->value ?? '6' }}">
                    </div>
                </div>

                <button type="submit" class="btn">Update Content Settings</button>
            </form>
        </div>
    </div>
</div>

<!-- Section Titles Tab -->
<div id="titles" class="tab-content">
    <div class="card">
        <div class="card-header">
            <h3>Section Titles & Descriptions</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.settings.titles') }}">
                @csrf
                
                <div class="settings-grid">
                    <div class="form-group">
                        <label for="blog_title">Blog Section Title</label>
                        <input type="text" id="blog_title" name="blog_title" class="form-control"
                               value="{{ $settings['blog']['blog_title']->value ?? 'Latest Tech News' }}">
                    </div>

                    <div class="form-group">
                        <label for="blog_description">Blog Section Description</label>
                        <textarea id="blog_description" name="blog_description" class="form-control" rows="3">{{ $settings['blog']['blog_description']->value ?? 'Stay updated with the latest technology trends and news' }}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="projects_title">Projects Section Title</label>
                        <input type="text" id="projects_title" name="projects_title" class="form-control"
                               value="{{ $settings['projects']['projects_title']->value ?? 'My Projects' }}">
                    </div>

                    <div class="form-group">
                        <label for="projects_description">Projects Section Description</label>
                        <textarea id="projects_description" name="projects_description" class="form-control" rows="3">{{ $settings['projects']['projects_description']->value ?? 'Here are some of my recent projects' }}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="services_title">Services Section Title</label>
                        <input type="text" id="services_title" name="services_title" class="form-control"
                               value="{{ $settings['services']['services_title']->value ?? 'My Services' }}">
                    </div>

                    <div class="form-group">
                        <label for="services_description">Services Section Description</label>
                        <textarea id="services_description" name="services_description" class="form-control" rows="3">{{ $settings['services']['services_description']->value ?? 'What I can do for you' }}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="testimonials_title">Testimonials Section Title</label>
                        <input type="text" id="testimonials_title" name="testimonials_title" class="form-control"
                               value="{{ $settings['testimonials']['testimonials_title']->value ?? 'What Clients Say' }}">
                    </div>

                    <div class="form-group">
                        <label for="testimonials_description">Testimonials Section Description</label>
                        <textarea id="testimonials_description" name="testimonials_description" class="form-control" rows="3">{{ $settings['testimonials']['testimonials_description']->value ?? 'Feedback from satisfied clients' }}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="footer_description">Footer Description</label>
                        <textarea id="footer_description" name="footer_description" class="form-control" rows="3">{{ $settings['footer']['footer_description']->value ?? 'Professional 2D/3D Artist and Web Developer creating amazing digital experiences.' }}</textarea>
                    </div>
                </div>

                <button type="submit" class="btn">Update Section Titles</button>
            </form>
        </div>
    </div>
</div>

<script>
    function showTab(tabName) {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => content.classList.remove('active'));
        
        // Remove active class from all buttons
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => button.classList.remove('active'));
        
        // Show selected tab content
        document.getElementById(tabName).classList.add('active');
        
        // Add active class to clicked button
        event.target.classList.add('active');
    }

    function updateColorPreview(colorId) {
        const colorInput = document.getElementById(colorId);
        const preview = document.getElementById(colorId + '_preview');
        const textInput = colorInput.parentElement.querySelector('input[type="text"]');
        
        preview.style.backgroundColor = colorInput.value;
        textInput.value = colorInput.value;
    }
</script>
@endsection
