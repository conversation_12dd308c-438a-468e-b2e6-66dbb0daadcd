<?php $__env->startSection('title', 'Hero Section Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Hero Section</h3>
            <?php if(!$hero): ?>
                <a href="<?php echo e(route('admin.hero.create')); ?>" class="btn btn-success">Create Hero Section</a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <?php if($hero): ?>
            <div style="display: grid; grid-template-columns: 1fr 300px; gap: 30px; align-items: start;">
                <!-- Hero Content -->
                <div>
                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #333; margin-bottom: 10px;"><?php echo e($hero->name); ?></h4>
                        <h5 style="color: #667eea; margin-bottom: 15px;"><?php echo e($hero->title); ?></h5>
                        <p style="color: #666; line-height: 1.6;"><?php echo e($hero->description); ?></p>
                    </div>

                    <!-- CTA Buttons -->
                    <?php if($hero->cta_primary_text || $hero->cta_secondary_text): ?>
                    <div style="margin-bottom: 20px;">
                        <h6 style="color: #333; margin-bottom: 10px;">Call-to-Action Buttons:</h6>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <?php if($hero->cta_primary_text): ?>
                                <a href="<?php echo e($hero->cta_primary_url); ?>" class="btn" style="pointer-events: none;">
                                    <?php echo e($hero->cta_primary_text); ?>

                                </a>
                            <?php endif; ?>
                            <?php if($hero->cta_secondary_text): ?>
                                <a href="<?php echo e($hero->cta_secondary_url); ?>" class="btn-outline" 
                                   style="padding: 10px 20px; border: 2px solid #667eea; color: #667eea; text-decoration: none; border-radius: 6px; pointer-events: none;">
                                    <?php echo e($hero->cta_secondary_text); ?>

                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Settings -->
                    <div style="margin-bottom: 20px;">
                        <h6 style="color: #333; margin-bottom: 10px;">Settings:</h6>
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <span style="color: #666;">Galaxy Animation:</span>
                            <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; <?php echo e($hero->show_galaxy_animation ? 'background-color: #d4edda; color: #155724;' : 'background-color: #f8d7da; color: #721c24;'); ?>">
                                <?php echo e($hero->show_galaxy_animation ? 'Enabled' : 'Disabled'); ?>

                            </span>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <a href="<?php echo e(route('admin.hero.edit', $hero)); ?>" class="btn">Edit Hero Section</a>
                        <form method="POST" action="<?php echo e(route('admin.hero.destroy', $hero)); ?>" 
                              style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this hero section?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger">Delete</button>
                        </form>
                    </div>
                </div>

                <!-- Hero Photo -->
                <div>
                    <?php if($hero->photo): ?>
                        <div style="text-align: center;">
                            <h6 style="color: #333; margin-bottom: 10px;">Profile Photo:</h6>
                            <img src="<?php echo e(asset($hero->photo)); ?>" alt="<?php echo e($hero->name); ?>" 
                                 style="width: 100%; max-width: 250px; height: 250px; object-fit: cover; border-radius: 50%; border: 4px solid #667eea;">
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px 20px; border: 2px dashed #ddd; border-radius: 8px;">
                            <p style="color: #666; margin: 0;">No photo uploaded</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: 40px 20px;">
                <div style="font-size: 48px; color: #667eea; margin-bottom: 20px;">🎯</div>
                <h4 style="color: #333; margin-bottom: 15px;">No Hero Section Found</h4>
                <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                    Create a hero section to showcase your name, title, and description on the homepage.
                </p>
                <a href="<?php echo e(route('admin.hero.create')); ?>" class="btn">Create Hero Section</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php if($hero): ?>
<div class="card">
    <div class="card-header">
        <h3>Preview</h3>
    </div>
    <div class="card-body">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 8px; text-align: center; position: relative; overflow: hidden;">
            <?php if($hero->show_galaxy_animation): ?>
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
                    <div style="position: absolute; width: 2px; height: 2px; background: white; border-radius: 50%; top: 20%; left: 20%; animation: twinkle 2s infinite;"></div>
                    <div style="position: absolute; width: 1px; height: 1px; background: white; border-radius: 50%; top: 30%; left: 80%; animation: twinkle 3s infinite 0.5s;"></div>
                    <div style="position: absolute; width: 3px; height: 3px; background: white; border-radius: 50%; top: 70%; left: 60%; animation: twinkle 2.5s infinite 1s;"></div>
                    <div style="position: absolute; width: 1.5px; height: 1.5px; background: white; border-radius: 50%; top: 80%; left: 30%; animation: twinkle 3.5s infinite 1.5s;"></div>
                    <div style="position: absolute; width: 2px; height: 2px; background: white; border-radius: 50%; top: 60%; left: 90%; animation: twinkle 2.8s infinite 2s;"></div>
                </div>
            <?php endif; ?>
            
            <div style="position: relative; z-index: 1;">
                <?php if($hero->photo): ?>
                    <img src="<?php echo e(asset($hero->photo)); ?>" alt="<?php echo e($hero->name); ?>" 
                         style="width: 120px; height: 120px; object-fit: cover; border-radius: 50%; border: 3px solid white; margin-bottom: 20px;">
                <?php endif; ?>
                
                <h1 style="font-size: 2.5rem; margin-bottom: 10px; font-weight: 700;"><?php echo e($hero->name); ?></h1>
                <h2 style="font-size: 1.5rem; margin-bottom: 20px; opacity: 0.9; font-weight: 400;"><?php echo e($hero->title); ?></h2>
                <p style="font-size: 1.1rem; margin-bottom: 30px; opacity: 0.8; max-width: 600px; margin-left: auto; margin-right: auto;"><?php echo e($hero->description); ?></p>
                
                <?php if($hero->cta_primary_text || $hero->cta_secondary_text): ?>
                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                    <?php if($hero->cta_primary_text): ?>
                        <span style="background: white; color: #667eea; padding: 12px 24px; border-radius: 6px; font-weight: 600; text-decoration: none;">
                            <?php echo e($hero->cta_primary_text); ?>

                        </span>
                    <?php endif; ?>
                    <?php if($hero->cta_secondary_text): ?>
                        <span style="border: 2px solid white; color: white; padding: 10px 22px; border-radius: 6px; font-weight: 600; text-decoration: none;">
                            <?php echo e($hero->cta_secondary_text); ?>

                        </span>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes twinkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}
</style>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\hero\index.blade.php ENDPATH**/ ?>