@extends('layouts.admin')

@section('title', 'Create Hero Section')

@section('content')
<div class="card">
    <div class="card-header">
        <h3>Create Hero Section</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('admin.hero.store') }}" enctype="multipart/form-data">
            @csrf
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="name">Name *</label>
                        <input type="text" id="name" name="name" class="form-control" 
                               value="{{ old('name') }}" required>
                        @error('name')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="title">Title/Profession *</label>
                        <input type="text" id="title" name="title" class="form-control" 
                               value="{{ old('title') }}" placeholder="e.g., 2D/3D Artist and Web Developer" required>
                        @error('title')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" class="form-control" rows="4" 
                                  required>{{ old('description') }}</textarea>
                        <small style="color: #666;">Brief description about yourself and what you do</small>
                        @error('description')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="photo">Profile Photo</label>
                        <input type="file" id="photo" name="photo" class="form-control" accept="image/*">
                        <small style="color: #666;">Upload a profile photo (JPEG, PNG, JPG, GIF - Max: 2MB)</small>
                        @error('photo')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <h5 style="margin-bottom: 20px; color: #333;">Call-to-Action Buttons</h5>
                    
                    <div class="form-group">
                        <label for="cta_primary_text">Primary Button Text</label>
                        <input type="text" id="cta_primary_text" name="cta_primary_text" class="form-control" 
                               value="{{ old('cta_primary_text') }}" placeholder="e.g., View My Work">
                        @error('cta_primary_text')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_primary_url">Primary Button URL</label>
                        <input type="text" id="cta_primary_url" name="cta_primary_url" class="form-control" 
                               value="{{ old('cta_primary_url') }}" placeholder="/projects">
                        @error('cta_primary_url')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_secondary_text">Secondary Button Text</label>
                        <input type="text" id="cta_secondary_text" name="cta_secondary_text" class="form-control" 
                               value="{{ old('cta_secondary_text') }}" placeholder="e.g., Contact Me">
                        @error('cta_secondary_text')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="cta_secondary_url">Secondary Button URL</label>
                        <input type="text" id="cta_secondary_url" name="cta_secondary_url" class="form-control" 
                               value="{{ old('cta_secondary_url') }}" placeholder="/contact">
                        @error('cta_secondary_url')
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" name="show_galaxy_animation" value="1" 
                                   {{ old('show_galaxy_animation', true) ? 'checked' : '' }}>
                            <span>Enable Galaxy Star Animation</span>
                        </label>
                        <small style="color: #666;">Show animated stars in the background of the hero section</small>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 30px;">
                <button type="submit" class="btn">Create Hero Section</button>
                <a href="{{ route('admin.hero.index') }}" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>
@endsection
