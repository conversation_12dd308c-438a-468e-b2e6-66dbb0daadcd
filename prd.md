Product Requirements Document (PRD)

## Project: <PERSON> Website

**Tech Stack**: <PERSON><PERSON> (latest), MySQL, Vanilla CSS (no CSS framework)  
**Authentication**: Custom (no Breeze/Jetstream) with modern login, register, and reset password pages.

---

## 1. Global Requirements

-   **Color Scheme**:

    -   No dark mode or dark colors.
    -   Default palette: Light, visually appealing, modern (soft blues, whites, light grays, pastel accents).
    -   Admin panel allows global **color customization** (primary, secondary, background, text).

-   **Images**:

    -   Stored in `/public/images`.
    -   Admin panel allows replacing images dynamically.

-   **Frontend Control**:

    -   Each section (Navbar, Hero, Services, Projects, Blog, Testimonials, Footer) must have an **admin toggle to show/hide**.

-   **Authentication**:
    -   Custom login, register, password reset (token-based), email verification.
    -   Role-based (Admin, Editor).
    -   Secure with CSRF and hashed passwords.

---

## 2. Admin Panel Structure

### Features

1. **Navbar Management**

    - CRUD for menu items.
    - Auto-generates linked standalone pages when menus are created (Services, Projects, Blog, Contact, etc.).
    - Customizable **menu order** and **background color** (distinct from page).
    - Show/Hide toggle per menu.

2. **Global Color Settings**

    - Update site-wide primary, secondary, text, and background colors.

3. **Hero Section Management**

    - Fields: Name, Title, Short Description, Photo (circular frame), CTA buttons (text + links).
    - Toggle for **galaxy star animation background**.
    - Enable/disable Hero section.

4. **Projects Section Management**

    - CRUD: Title, Description, Image, URL.
    - Set **homepage project count** (default 6).
    - Editable section title and description.

5. **Services Section Management**

    - CRUD: Title, Description, Icon/Image.
    - Set **homepage service count** (default 6).
    - Editable title and description.

6. **Projects Stats Management**

    - Four editable counters:
        - Web Design/Development
        - 3D Works
        - Graphic Works
        - Ongoing Projects
    - Manually adjustable numbers.

7. **Blog Section Management**

    - Fetches **tech news via external API** (key from `api.txt`).
    - Secure API key storage (not public).
    - Editable title, description, and **homepage post count** (default 6).
    - API key update via admin panel.

8. **Testimonials Section Management**

    - CRUD for testimonials: Name, Image, Quote, Role.
    - Animated **carousel (left-to-right)** with smooth transitions.
    - Editable title and description.

9. **Footer Management**

    - Editable short description, useful links, social links, navbar links.
    - Auto-updating copyright:
        ```
        © YEAR Pixel and Code. All Rights Reserved. Developed by Aziz Khan.
        ```
    - Editable background and text colors.

10. **Visibility Toggles**
    - Show/Hide toggles for **all sections**.

---

## 3. Frontend Sections

1. **Navbar**

    - Default items: Home, Services, Projects, Blog, Contact.
    - CRUD-driven (from Admin).
    - Distinct **background color**.

2. **Hero Section**

    - Left: Aziz Khan name, “2D/3D Artist and Web Developer”, short description, two CTA buttons (“View Projects”, “Let’s Get Started”).
    - Right: Circular photo frame (image from `/public/images`).
    - Background: Smooth **galaxy-themed star animation**.
    - Fully configurable via Admin.

3. **Projects Section**

    - Display **6 projects** by default, with “View All Projects”.
    - Admin sets project count, title, description.
    - CRUD-managed projects.

4. **Services Section**

    - Display **6 services** by default, with “View All Services”.
    - CRUD-managed services, editable title and description.

5. **Projects Stats Section**

    - Four counters (editable via Admin).

6. **Blog Section**

    - Fetches external tech news via secure API key.
    - Display **6 posts** by default, with “View All Blog Posts”.
    - Editable title, description, API key, and post count.

7. **Testimonials Section**

    - Animated **carousel** with smooth transitions.
    - CRUD testimonials, editable title and description.

8. **Footer Section**
    - Short description, useful links, social links, navbar menu links.
    - Auto-updating copyright.

---

## 4. Backend Functionalities

-   **Admin Dashboard**: Stats overview (projects, services, blog posts fetched, testimonials).
-   **Content CRUD** for all dynamic sections.
-   **Color and Style Settings** for site-wide customization.
-   **Authentication**: Secure, role-based, custom password reset.
-   **Security**: API keys in `.env`, CSRF protection.

---

## 5. Non-Functional Requirements

-   **Performance**: Optimized queries, pagination for large lists.
-   **Responsiveness**: Custom CSS, mobile-first.
-   **Accessibility**: High-contrast, keyboard navigation.
-   **SEO**: Editable metadata per page (via Admin).
-   **Scalability**: Modular controllers for each section.
