<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings Test</title>
    <?php echo $__env->make('components.dynamic-styles', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</head>
<body>
    <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h1 class="section-title">Settings Test Page</h1>
        
        <div class="card" style="padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
            <h2>Color Settings</h2>
            <p>Primary Color: <?php echo e($site_colors['primary']); ?></p>
            <p>Secondary Color: <?php echo e($site_colors['secondary']); ?></p>
            <p>Background Color: <?php echo e($site_colors['background']); ?></p>
            <p>Text Color: <?php echo e($site_colors['text']); ?></p>
        </div>

        <div class="card" style="padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
            <h2>Section Visibility</h2>
            <?php $__currentLoopData = $section_visibility; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section => $visible): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <p><?php echo e(ucfirst($section)); ?>: <?php echo e($visible ? 'Visible' : 'Hidden'); ?></p>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="card" style="padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
            <h2>Content Counts</h2>
            <?php $__currentLoopData = $content_counts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <p><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?>: <?php echo e($count); ?></p>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div style="margin: 20px 0;">
            <button class="btn" style="padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer;">Primary Button</button>
            <button class="btn-outline" style="padding: 10px 20px; border: 2px solid; border-radius: 6px; cursor: pointer; margin-left: 10px;">Outline Button</button>
        </div>

        <div style="margin: 20px 0;">
            <a href="<?php echo e(route('admin.settings.index')); ?>">Go to Admin Settings</a>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\test-settings.blade.php ENDPATH**/ ?>