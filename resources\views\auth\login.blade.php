@extends('layouts.auth')

@section('title', 'Login')

@section('content')
<div class="auth-header">
    <h1>Welcome Back</h1>
    <p>Sign in to your account</p>
</div>

@if ($errors->any())
    <div class="alert alert-danger">
        @foreach ($errors->all() as $error)
            <div>{{ $error }}</div>
        @endforeach
    </div>
@endif

@if (session('status'))
    <div class="alert alert-success">
        {{ session('status') }}
    </div>
@endif

<form method="POST" action="{{ route('login') }}">
    @csrf
    
    <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" id="email" name="email" class="form-control" value="{{ old('email') }}" required autofocus>
    </div>
    
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" class="form-control" required>
    </div>
    
    <div class="checkbox-group">
        <input type="checkbox" id="remember" name="remember">
        <label for="remember">Remember me</label>
    </div>
    
    <button type="submit" class="btn">Sign In</button>
</form>

<div class="auth-links">
    <a href="{{ route('password.request') }}">Forgot your password?</a>
    <br><br>
    <a href="{{ route('register') }}">Don't have an account? Sign up</a>
</div>
@endsection
