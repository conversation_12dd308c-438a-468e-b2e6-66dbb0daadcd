/* Responsive Portfolio Styles - Mobile First Approach */

/* Base styles for mobile (320px+) */
.container {
    max-width: 100%;
    padding: 0 1rem;
}

/* Navigation */
.navbar {
    background: var(--navbar-background, rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 0.75rem 0;
    transition: all 0.3s ease;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.logo {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-links {
    display: none; /* Hidden on mobile */
    list-style: none;
    gap: 1.5rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s ease;
    font-size: 0.9rem;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
}

/* Mobile menu */
.mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1rem;
}

.mobile-menu.active {
    display: block;
}

.mobile-menu a {
    display: block;
    padding: 0.75rem 0;
    text-decoration: none;
    color: var(--text-color);
    border-bottom: 1px solid #eee;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 6rem 0 2rem;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
    width: 100%;
}

.hero h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero .subtitle {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero .description {
    font-size: 1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    min-width: 200px;
}

.btn-primary {
    background: white;
    color: var(--primary-color);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

/* Sections */
.section {
    padding: 3rem 0;
}

.section-title {
    text-align: center;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: var(--text-color);
}

/* Grid layouts */
.grid {
    display: grid;
    gap: 1.5rem;
}

.projects-grid,
.services-grid,
.testimonials-grid,
.blog-grid {
    grid-template-columns: 1fr;
}

.stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

/* Cards */
.card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.project-card,
.service-card,
.testimonial-card,
.blog-card {
    padding: 1.5rem;
}

.project-image,
.blog-image {
    width: 100%;
    height: 180px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.project-image img,
.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.service-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    text-align: center;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Stats Section */
.stats {
    background: var(--primary-color);
    color: white;
}

.stat-card {
    text-align: center;
    padding: 1.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Testimonials */
.testimonial-quote {
    font-style: italic;
    margin-bottom: 1rem;
    font-size: 1rem;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.testimonial-info h4 {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.testimonial-info p {
    color: #666;
    font-size: 0.8rem;
    margin: 0;
}

.testimonial-rating {
    margin-bottom: 1rem;
    color: #ffc107;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 2rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.footer-section p,
.footer-section a {
    color: #ccc;
    text-decoration: none;
    line-height: 1.6;
    font-size: 0.9rem;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #555;
    color: #999;
    font-size: 0.8rem;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1001;
}

.skip-link:focus {
    top: 6px;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Tablet styles (768px+) */
@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
    
    .nav-links {
        display: flex;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero .subtitle {
        font-size: 1.25rem;
    }
    
    .cta-buttons {
        flex-direction: row;
        justify-content: center;
    }
    
    .section {
        padding: 4rem 0;
    }
    
    .section-title {
        font-size: 2.25rem;
    }
    
    .projects-grid,
    .services-grid,
    .blog-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
}

/* Desktop styles (1024px+) */
@media (min-width: 1024px) {
    .container {
        max-width: 1200px;
        padding: 0 2rem;
    }
    
    .hero h1 {
        font-size: 3.5rem;
    }
    
    .hero .subtitle {
        font-size: 1.5rem;
    }
    
    .section {
        padding: 5rem 0;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
    
    .projects-grid,
    .services-grid,
    .blog-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .testimonials-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .stat-number {
        font-size: 3rem;
    }
}

/* Large desktop styles (1280px+) */
@media (min-width: 1280px) {
    .projects-grid,
    .services-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Print styles */
@media print {
    .navbar,
    .cta-buttons,
    .mobile-menu-toggle {
        display: none;
    }
    
    .hero {
        min-height: auto;
        padding: 2rem 0;
    }
    
    .section {
        padding: 2rem 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid var(--text-color);
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
