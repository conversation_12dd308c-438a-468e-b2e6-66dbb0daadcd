<?php

namespace Database\Factories;

use App\Models\NavbarItem;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class NavbarItemFactory extends Factory
{
    protected $model = NavbarItem::class;

    public function definition()
    {
        $title = $this->faker->randomElement([
            'Home', 'About', 'Services', 'Projects', 'Blog', 'Contact'
        ]);
        
        return [
            'title' => $title,
            'slug' => Str::slug($title),
            'url' => '/' . Str::slug($title),
            'is_active' => $this->faker->boolean(80),
            'order' => $this->faker->numberBetween(1, 100),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }
}
