<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    public function index()
    {
        $settings = [
            'colors' => SiteSetting::where('group', 'colors')->get()->keyBy('key'),
            'sections' => SiteSetting::where('group', 'sections')->get()->keyBy('key'),
            'content' => SiteSetting::where('group', 'content')->get()->keyBy('key'),
            'blog' => SiteSetting::where('group', 'blog')->get()->keyBy('key'),
            'projects' => SiteSetting::where('group', 'projects')->get()->keyBy('key'),
            'services' => SiteSetting::where('group', 'services')->get()->keyBy('key'),
            'testimonials' => SiteSetting::where('group', 'testimonials')->get()->keyBy('key'),
            'footer' => SiteSetting::where('group', 'footer')->get()->keyBy('key'),
        ];

        return view('admin.settings.index', compact('settings'));
    }

    public function updateColors(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'primary_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'secondary_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'background_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'text_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $colors = [
            'primary_color' => $request->primary_color,
            'secondary_color' => $request->secondary_color,
            'background_color' => $request->background_color,
            'text_color' => $request->text_color,
        ];

        foreach ($colors as $key => $value) {
            SiteSetting::set($key, $value, 'color', 'colors');
        }

        return back()->with('success', 'Color settings updated successfully!');
    }

    public function updateSections(Request $request)
    {
        $sections = [
            'show_navbar',
            'show_hero',
            'show_services',
            'show_projects',
            'show_stats',
            'show_blog',
            'show_testimonials',
            'show_footer'
        ];

        foreach ($sections as $section) {
            $value = $request->has($section) ? '1' : '0';
            SiteSetting::set($section, $value, 'boolean', 'sections');
        }

        return back()->with('success', 'Section visibility settings updated successfully!');
    }

    public function updateContent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'homepage_projects_count' => 'required|integer|min:1|max:20',
            'homepage_services_count' => 'required|integer|min:1|max:20',
            'homepage_blog_count' => 'required|integer|min:1|max:20',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $contentSettings = [
            'homepage_projects_count' => $request->homepage_projects_count,
            'homepage_services_count' => $request->homepage_services_count,
            'homepage_blog_count' => $request->homepage_blog_count,
        ];

        foreach ($contentSettings as $key => $value) {
            SiteSetting::set($key, $value, 'number', 'content');
        }

        return back()->with('success', 'Content count settings updated successfully!');
    }

    public function updateSectionTitles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'blog_title' => 'required|string|max:255',
            'blog_description' => 'required|string|max:500',
            'projects_title' => 'required|string|max:255',
            'projects_description' => 'required|string|max:500',
            'services_title' => 'required|string|max:255',
            'services_description' => 'required|string|max:500',
            'testimonials_title' => 'required|string|max:255',
            'testimonials_description' => 'required|string|max:500',
            'footer_description' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $sectionSettings = [
            'blog_title' => ['value' => $request->blog_title, 'group' => 'blog'],
            'blog_description' => ['value' => $request->blog_description, 'group' => 'blog'],
            'projects_title' => ['value' => $request->projects_title, 'group' => 'projects'],
            'projects_description' => ['value' => $request->projects_description, 'group' => 'projects'],
            'services_title' => ['value' => $request->services_title, 'group' => 'services'],
            'services_description' => ['value' => $request->services_description, 'group' => 'services'],
            'testimonials_title' => ['value' => $request->testimonials_title, 'group' => 'testimonials'],
            'testimonials_description' => ['value' => $request->testimonials_description, 'group' => 'testimonials'],
            'footer_description' => ['value' => $request->footer_description, 'group' => 'footer'],
        ];

        foreach ($sectionSettings as $key => $data) {
            SiteSetting::set($key, $data['value'], 'text', $data['group']);
        }

        return back()->with('success', 'Section titles and descriptions updated successfully!');
    }

    public function generateCss()
    {
        $colors = SiteSetting::where('group', 'colors')->get()->keyBy('key');
        
        $css = ":root {\n";
        $css .= "    --primary-color: " . ($colors['primary_color']->value ?? '#3b82f6') . ";\n";
        $css .= "    --secondary-color: " . ($colors['secondary_color']->value ?? '#64748b') . ";\n";
        $css .= "    --background-color: " . ($colors['background_color']->value ?? '#ffffff') . ";\n";
        $css .= "    --text-color: " . ($colors['text_color']->value ?? '#1f2937') . ";\n";
        $css .= "}\n";

        return response($css)->header('Content-Type', 'text/css');
    }
}
