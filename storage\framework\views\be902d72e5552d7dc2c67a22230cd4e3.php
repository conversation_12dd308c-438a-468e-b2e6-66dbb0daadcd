<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number"><?php echo e($stats['projects']); ?></div>
        <div class="stat-label">Projects</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo e($stats['services']); ?></div>
        <div class="stat-label">Services</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo e($stats['testimonials']); ?></div>
        <div class="stat-label">Testimonials</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo e($stats['users']); ?></div>
        <div class="stat-label">Users</div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Welcome to Admin Panel</h3>
    </div>
    <div class="card-body">
        <p>Welcome to the Aziz Khan Portfolio admin panel. Here you can manage all aspects of your portfolio website including:</p>
        
        <ul style="margin: 20px 0; padding-left: 20px;">
            <li>Hero section content and settings</li>
            <li>Projects and services management</li>
            <li>Testimonials and project statistics</li>
            <li>Site-wide color and visibility settings</li>
            <li>Navigation menu management</li>
            <li>Blog API configuration</li>
            <?php if(auth()->user()->isAdmin()): ?>
            <li>User management and permissions</li>
            <?php endif; ?>
        </ul>
        
        <p>Use the navigation menu on the left to access different sections of the admin panel.</p>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Quick Actions</h3>
    </div>
    <div class="card-body">
        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
            <a href="<?php echo e(route('admin.projects.create')); ?>" class="btn btn-success">Add New Project</a>
            <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-success">Add New Service</a>
            <a href="<?php echo e(route('admin.testimonials.create')); ?>" class="btn btn-success">Add Testimonial</a>
            <a href="<?php echo e(route('admin.settings.index')); ?>" class="btn">Site Settings</a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\dashboard.blade.php ENDPATH**/ ?>