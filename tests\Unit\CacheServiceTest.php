<?php

namespace Tests\Unit;

use App\Services\CacheService;
use App\Models\Setting;
use App\Models\NavbarItem;
use App\Models\Project;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CacheServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Cache::flush();
    }

    public function test_get_settings_caches_data()
    {
        Setting::create(['key' => 'site_name', 'value' => 'Test Site']);
        Setting::create(['key' => 'primary_color', 'value' => '#123456']);
        
        // First call should hit database
        $settings1 = CacheService::getSettings();
        
        // Second call should hit cache
        $settings2 = CacheService::getSettings();
        
        $this->assertEquals($settings1, $settings2);
        $this->assertEquals('Test Site', $settings1['site_name']);
        $this->assertEquals('#123456', $settings1['primary_color']);
        
        // Verify cache was used
        $this->assertTrue(Cache::has('portfolio_settings'));
    }

    public function test_get_navbar_items_returns_active_only()
    {
        NavbarItem::factory()->create(['title' => 'Active Item', 'is_active' => true, 'order' => 1]);
        NavbarItem::factory()->create(['title' => 'Inactive Item', 'is_active' => false, 'order' => 2]);
        
        $items = CacheService::getNavbarItems();
        
        $this->assertCount(1, $items);
        $this->assertEquals('Active Item', $items->first()->title);
    }

    public function test_get_featured_projects_respects_limit()
    {
        Project::factory()->count(10)->create([
            'is_active' => true,
            'is_featured' => true,
        ]);
        
        $projects = CacheService::getFeaturedProjects(5);
        
        $this->assertCount(5, $projects);
    }

    public function test_get_active_services_filters_correctly()
    {
        Service::factory()->create(['title' => 'Active Service', 'is_active' => true]);
        Service::factory()->create(['title' => 'Inactive Service', 'is_active' => false]);
        
        $services = CacheService::getActiveServices();
        
        $this->assertCount(1, $services);
        $this->assertEquals('Active Service', $services->first()->title);
    }

    public function test_cache_clear_all_removes_all_caches()
    {
        // Set up some cached data
        CacheService::getSettings();
        CacheService::getNavbarItems();
        
        $this->assertTrue(Cache::has('portfolio_settings'));
        $this->assertTrue(Cache::has('navbar_items'));
        
        CacheService::clearAll();
        
        // Cache should be cleared
        $this->assertFalse(Cache::has('portfolio_settings'));
        $this->assertFalse(Cache::has('navbar_items'));
    }

    public function test_cache_clear_by_type_removes_specific_cache()
    {
        // Set up cached data
        CacheService::getSettings();
        CacheService::getNavbarItems();
        
        CacheService::clearByType('settings');
        
        $this->assertFalse(Cache::has('portfolio_settings'));
        // Other caches might still exist (depending on implementation)
    }

    public function test_warm_up_preloads_critical_caches()
    {
        Setting::factory()->create();
        NavbarItem::factory()->create(['is_active' => true]);
        Project::factory()->create(['is_active' => true, 'is_featured' => true]);
        
        CacheService::warmUp();
        
        $this->assertTrue(Cache::has('portfolio_settings'));
        $this->assertTrue(Cache::has('navbar_items'));
    }

    public function test_cache_ttl_is_respected()
    {
        Setting::create(['key' => 'test', 'value' => 'original']);
        
        // Get cached value
        $settings1 = CacheService::getSettings();
        $this->assertEquals('original', $settings1['test']);
        
        // Update database
        Setting::where('key', 'test')->update(['value' => 'updated']);
        
        // Should still return cached value
        $settings2 = CacheService::getSettings();
        $this->assertEquals('original', $settings2['test']);
        
        // Clear cache and get fresh value
        Cache::forget('portfolio_settings');
        $settings3 = CacheService::getSettings();
        $this->assertEquals('updated', $settings3['test']);
    }

    public function test_get_stats_returns_performance_data()
    {
        $stats = CacheService::getStats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('memory_usage', $stats);
        $this->assertArrayHasKey('peak_memory', $stats);
        $this->assertIsInt($stats['memory_usage']);
        $this->assertIsInt($stats['peak_memory']);
    }

    public function test_blog_posts_cache_handles_api_failure()
    {
        // Mock settings without API key
        Setting::create(['key' => 'blog_api_key', 'value' => '']);
        
        $posts = CacheService::getBlogPosts();
        
        $this->assertIsArray($posts);
        $this->assertEmpty($posts);
    }

    public function test_cache_keys_are_unique_for_different_limits()
    {
        Project::factory()->count(10)->create([
            'is_active' => true,
            'is_featured' => true,
        ]);
        
        $projects5 = CacheService::getFeaturedProjects(5);
        $projects3 = CacheService::getFeaturedProjects(3);
        
        $this->assertCount(5, $projects5);
        $this->assertCount(3, $projects3);
        
        // Verify different cache keys were used
        $this->assertTrue(Cache::has('featured_projects_5'));
        $this->assertTrue(Cache::has('featured_projects_3'));
    }
}
