<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings Test</title>
    @include('components.dynamic-styles')
</head>
<body>
    <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h1 class="section-title">Settings Test Page</h1>
        
        <div class="card" style="padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
            <h2>Color Settings</h2>
            <p>Primary Color: {{ $site_colors['primary'] }}</p>
            <p>Secondary Color: {{ $site_colors['secondary'] }}</p>
            <p>Background Color: {{ $site_colors['background'] }}</p>
            <p>Text Color: {{ $site_colors['text'] }}</p>
        </div>

        <div class="card" style="padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
            <h2>Section Visibility</h2>
            @foreach($section_visibility as $section => $visible)
                <p>{{ ucfirst($section) }}: {{ $visible ? 'Visible' : 'Hidden' }}</p>
            @endforeach
        </div>

        <div class="card" style="padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
            <h2>Content Counts</h2>
            @foreach($content_counts as $key => $count)
                <p>{{ ucfirst(str_replace('_', ' ', $key)) }}: {{ $count }}</p>
            @endforeach
        </div>

        <div style="margin: 20px 0;">
            <button class="btn" style="padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer;">Primary Button</button>
            <button class="btn-outline" style="padding: 10px 20px; border: 2px solid; border-radius: 6px; cursor: pointer; margin-left: 10px;">Outline Button</button>
        </div>

        <div style="margin: 20px 0;">
            <a href="{{ route('admin.settings.index') }}">Go to Admin Settings</a>
        </div>
    </div>
</body>
</html>
