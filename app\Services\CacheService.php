<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CacheService
{
    const CACHE_TTL = 3600; // 1 hour
    const LONG_CACHE_TTL = 86400; // 24 hours

    /**
     * Cache settings data
     */
    public static function getSettings()
    {
        return Cache::remember('portfolio_settings', self::LONG_CACHE_TTL, function () {
            return \App\Models\SiteSetting::pluck('value', 'key')->toArray();
        });
    }

    /**
     * Cache navbar items
     */
    public static function getNavbarItems()
    {
        return Cache::remember('navbar_items', self::CACHE_TTL, function () {
            return \App\Models\NavbarItem::where('is_active', true)
                ->orderBy('order')
                ->get();
        });
    }

    /**
     * Cache hero section
     */
    public static function getHeroSection()
    {
        return Cache::remember('hero_section', self::CACHE_TTL, function () {
            return \App\Models\HeroSection::where('is_active', true)->first();
        });
    }

    /**
     * Cache featured projects
     */
    public static function getFeaturedProjects($limit = 6)
    {
        return Cache::remember("featured_projects_{$limit}", self::CACHE_TTL, function () use ($limit) {
            return \App\Models\Project::where('is_active', true)
                ->where('is_featured', true)
                ->orderBy('order')
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Cache active services
     */
    public static function getActiveServices($limit = null)
    {
        $cacheKey = $limit ? "active_services_{$limit}" : 'active_services_all';

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($limit) {
            $query = \App\Models\Service::where('is_active', true)->orderBy('order');

            if ($limit) {
                $query->limit($limit);
            }

            return $query->get();
        });
    }

    /**
     * Cache project stats
     */
    public static function getProjectStats()
    {
        return Cache::remember('project_stats', self::CACHE_TTL, function () {
            return \App\Models\ProjectStat::orderBy('order')->get();
        });
    }

    /**
     * Cache testimonials
     */
    public static function getTestimonials($limit = null)
    {
        $cacheKey = $limit ? "testimonials_{$limit}" : 'testimonials_all';

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($limit) {
            $query = \App\Models\Testimonial::where('is_active', true)->orderBy('order');

            if ($limit) {
                $query->limit($limit);
            }

            return $query->get();
        });
    }

    /**
     * Cache blog posts from API
     */
    public static function getBlogPosts($limit = 10)
    {
        return Cache::remember("blog_posts_{$limit}", self::CACHE_TTL, function () use ($limit) {
            $settings = self::getSettings();
            $apiKey = $settings['blog_api_key'] ?? null;

            if (!$apiKey) {
                return [];
            }

            try {
                $response = Http::timeout(10)->get('https://newsapi.org/v2/everything', [
                    'q' => 'technology OR programming OR web development',
                    'language' => 'en',
                    'sortBy' => 'publishedAt',
                    'pageSize' => $limit,
                    'apiKey' => $apiKey,
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return collect($data['articles'] ?? [])->map(function ($article) {
                        return [
                            'title' => $article['title'],
                            'description' => $article['description'],
                            'url' => $article['url'],
                            'image' => $article['urlToImage'] ?? 'https://via.placeholder.com/400x200',
                            'published_at' => date('M d, Y', strtotime($article['publishedAt'])),
                            'source' => $article['source']['name'] ?? 'Unknown',
                            'slug' => Str::slug($article['title'])
                        ];
                    })->toArray();
                }
            } catch (\Exception $e) {
                Log::error('Blog API Error: ' . $e->getMessage());
            }

            return [];
        });
    }

    /**
     * Clear all portfolio caches
     */
    public static function clearAll()
    {
        $keys = [
            'portfolio_settings',
            'navbar_items',
            'hero_section',
            'featured_projects_*',
            'active_services_*',
            'project_stats',
            'testimonials_*',
            'blog_posts_*'
        ];

        foreach ($keys as $key) {
            if (str_contains($key, '*')) {
                // Clear pattern-based keys - for simplicity, flush all cache
                Cache::flush();
            } else {
                Cache::forget($key);
            }
        }
    }

    /**
     * Clear specific cache by type
     */
    public static function clearByType($type)
    {
        switch ($type) {
            case 'settings':
                Cache::forget('portfolio_settings');
                break;
            case 'navbar':
                Cache::forget('navbar_items');
                break;
            case 'hero':
                Cache::forget('hero_section');
                break;
            case 'projects':
                Cache::flush(); // Clear all project-related caches
                break;
            case 'services':
                Cache::flush(); // Clear all service-related caches
                break;
            case 'stats':
                Cache::forget('project_stats');
                break;
            case 'testimonials':
                Cache::flush(); // Clear all testimonial-related caches
                break;
            case 'blog':
                Cache::flush(); // Clear all blog-related caches
                break;
        }
    }

    /**
     * Warm up critical caches
     */
    public static function warmUp()
    {
        self::getSettings();
        self::getNavbarItems();
        self::getHeroSection();
        self::getFeaturedProjects();
        self::getActiveServices(6);
        self::getProjectStats();
        self::getTestimonials(6);
        self::getBlogPosts(6);
    }

    /**
     * Get cache statistics
     */
    public static function getStats()
    {
        return [
            'total_queries' => DB::getQueryLog(),
            'cache_hits' => 'N/A', // Would need Redis or Memcached for detailed stats
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }
}
