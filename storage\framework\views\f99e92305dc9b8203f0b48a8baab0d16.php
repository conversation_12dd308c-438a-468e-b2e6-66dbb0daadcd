<?php $__env->startSection('title', 'Blog API Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3>Blog API Settings</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="<?php echo e(route('admin.blog.settings.update')); ?>">
            <?php echo csrf_field(); ?>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="api_key">API Key *</label>
                        <input type="password" id="api_key" name="api_key" class="form-control" 
                               value="<?php echo e(old('api_key', $settings->api_key ?? '')); ?>" required>
                        <small style="color: #666;">Your NewsAPI.org API key</small>
                        <?php $__errorArgs = ['api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="api_url">API URL *</label>
                        <input type="url" id="api_url" name="api_url" class="form-control" 
                               value="<?php echo e(old('api_url', $settings->api_url ?? 'https://newsapi.org/v2/top-headlines')); ?>" required>
                        <small style="color: #666;">NewsAPI endpoint URL</small>
                        <?php $__errorArgs = ['api_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="posts_count">Number of Posts *</label>
                        <input type="number" id="posts_count" name="posts_count" class="form-control" 
                               value="<?php echo e(old('posts_count', $settings->posts_count ?? 6)); ?>" min="1" max="20" required>
                        <small style="color: #666;">How many articles to fetch (1-20)</small>
                        <?php $__errorArgs = ['posts_count'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="cache_duration">Cache Duration (minutes) *</label>
                        <input type="number" id="cache_duration" name="cache_duration" class="form-control" 
                               value="<?php echo e(old('cache_duration', $settings->cache_duration ?? 60)); ?>" min="5" max="1440" required>
                        <small style="color: #666;">How long to cache articles (5-1440 minutes)</small>
                        <?php $__errorArgs = ['cache_duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" name="is_active" value="1" 
                                   <?php echo e(old('is_active', $settings->is_active ?? true) ? 'checked' : ''); ?>>
                            <span>Enable Blog API</span>
                        </label>
                        <small style="color: #666;">Uncheck to disable blog functionality</small>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <h5 style="margin-bottom: 20px; color: #333;">API Setup Guide</h5>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h6 style="color: #333; margin-bottom: 15px;">📝 How to get NewsAPI Key:</h6>
                        <ol style="color: #666; line-height: 1.6;">
                            <li>Visit <a href="https://newsapi.org" target="_blank" style="color: #667eea;">newsapi.org</a></li>
                            <li>Click "Get API Key" and register</li>
                            <li>Verify your email address</li>
                            <li>Copy your API key from the dashboard</li>
                            <li>Paste it in the API Key field above</li>
                        </ol>
                    </div>
                    
                    <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h6 style="color: #0066cc; margin-bottom: 15px;">ℹ️ API Limits:</h6>
                        <ul style="color: #0066cc; line-height: 1.6;">
                            <li><strong>Free Plan:</strong> 1,000 requests/month</li>
                            <li><strong>Rate Limit:</strong> 1 request per second</li>
                            <li><strong>Categories:</strong> Technology articles only</li>
                            <li><strong>Language:</strong> English articles</li>
                        </ul>
                    </div>
                    
                    <div style="background: #fff3cd; padding: 20px; border-radius: 8px;">
                        <h6 style="color: #856404; margin-bottom: 15px;">⚠️ Important Notes:</h6>
                        <ul style="color: #856404; line-height: 1.6;">
                            <li>Cache duration helps reduce API calls</li>
                            <li>Higher cache = fewer API requests</li>
                            <li>Test connection before saving</li>
                            <li>Keep your API key secure</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 30px;">
                <button type="button" onclick="testConnection()" class="btn" style="background: #17a2b8; color: white;" id="test-btn">
                    🔗 Test Connection
                </button>
                <button type="submit" class="btn">💾 Save Settings</button>
                <a href="<?php echo e(route('admin.blog.index')); ?>" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>

<script>
function testConnection() {
    const apiKey = document.getElementById('api_key').value;
    const apiUrl = document.getElementById('api_url').value;
    const btn = document.getElementById('test-btn');
    
    if (!apiKey || !apiUrl) {
        alert('Please enter both API Key and API URL before testing.');
        return;
    }
    
    const originalText = btn.innerHTML;
    btn.innerHTML = '🔄 Testing...';
    btn.disabled = true;
    
    fetch('<?php echo e(route("admin.blog.test")); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            api_key: apiKey,
            api_url: apiUrl
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ Connection test failed. Please check your settings.');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\blog\settings.blade.php ENDPATH**/ ?>