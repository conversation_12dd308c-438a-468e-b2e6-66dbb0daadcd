<?php $__env->startSection('title', 'Blog Management'); ?>

<?php $__env->startSection('content'); ?>
<style>
    .blog-posts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .blog-post-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .blog-post-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .blog-post-image {
        width: 100%;
        height: 180px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        font-size: 48px;
    }

    .blog-post-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .blog-post-content {
        padding: 20px;
    }

    .blog-post-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .blog-post-description {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 15px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .blog-post-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #888;
        margin-bottom: 15px;
    }

    .blog-post-source {
        background: #e9ecef;
        padding: 2px 6px;
        border-radius: 3px;
        font-weight: 500;
    }

    .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .status-error {
        background: #fff3cd;
        color: #856404;
    }
</style>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Blog Management</h3>
            <div style="display: flex; gap: 10px;">
                <?php if($settings && $settings->is_active): ?>
                    <button onclick="refreshPosts()" class="btn btn-success" id="refresh-btn">
                        🔄 Refresh Posts
                    </button>
                <?php endif; ?>
                <a href="<?php echo e(route('admin.blog.settings')); ?>" class="btn">⚙️ Settings</a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Status Section -->
        <div style="margin-bottom: 30px;">
            <?php if(!$settings): ?>
                <div class="status-indicator status-inactive">
                    ❌ Blog API not configured
                </div>
                <p style="color: #666; margin-top: 10px;">
                    Configure your blog API settings to start fetching tech news articles.
                </p>
            <?php elseif(!$settings->is_active): ?>
                <div class="status-indicator status-inactive">
                    ⏸️ Blog API is disabled
                </div>
                <p style="color: #666; margin-top: 10px;">
                    Blog API is configured but currently disabled. Enable it in settings to fetch articles.
                </p>
            <?php elseif($error): ?>
                <div class="status-indicator status-error">
                    ⚠️ API Error
                </div>
                <p style="color: #856404; margin-top: 10px;"><?php echo e($error); ?></p>
            <?php else: ?>
                <div class="status-indicator status-active">
                    ✅ Blog API is active
                </div>
                <p style="color: #666; margin-top: 10px;">
                    Fetching <?php echo e($settings->posts_count); ?> latest tech articles. Cache refreshes every <?php echo e($settings->cache_duration); ?> minutes.
                </p>
            <?php endif; ?>
        </div>

        <!-- Blog Posts -->
        <?php if(count($posts) > 0): ?>
            <h5 style="margin-bottom: 20px; color: #333;">Latest Tech Articles (<?php echo e(count($posts)); ?>)</h5>
            
            <div class="blog-posts-grid">
                <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="blog-post-card">
                    <div class="blog-post-image">
                        <?php if($post['image']): ?>
                            <img src="<?php echo e($post['image']); ?>" alt="<?php echo e($post['title']); ?>" 
                                 onerror="this.parentElement.innerHTML='📰'">
                        <?php else: ?>
                            📰
                        <?php endif; ?>
                    </div>
                    
                    <div class="blog-post-content">
                        <div class="blog-post-title"><?php echo e($post['title']); ?></div>
                        
                        <?php if($post['description']): ?>
                            <div class="blog-post-description"><?php echo e($post['description']); ?></div>
                        <?php endif; ?>
                        
                        <div class="blog-post-meta">
                            <span class="blog-post-source"><?php echo e($post['source']); ?></span>
                            <?php if($post['published_at']): ?>
                                <span><?php echo e(\Carbon\Carbon::parse($post['published_at'])->diffForHumans()); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <a href="<?php echo e($post['url']); ?>" target="_blank" class="btn btn-sm" 
                           style="width: 100%; text-align: center;">
                            Read Article →
                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php elseif($settings && $settings->is_active && !$error): ?>
            <div style="text-align: center; padding: 40px 20px;">
                <div style="font-size: 48px; color: #667eea; margin-bottom: 20px;">📰</div>
                <h4 style="color: #333; margin-bottom: 15px;">No Articles Found</h4>
                <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                    No tech articles could be fetched at this time. Try refreshing or check your API settings.
                </p>
                <button onclick="refreshPosts()" class="btn">🔄 Try Again</button>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: 40px 20px;">
                <div style="font-size: 48px; color: #667eea; margin-bottom: 20px;">📰</div>
                <h4 style="color: #333; margin-bottom: 15px;">Blog API Setup Required</h4>
                <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                    Configure your blog API settings to start displaying the latest tech news on your portfolio.
                </p>
                <a href="<?php echo e(route('admin.blog.settings')); ?>" class="btn">⚙️ Configure API</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function refreshPosts() {
    const btn = document.getElementById('refresh-btn');
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '🔄 Refreshing...';
    btn.disabled = true;
    
    fetch('<?php echo e(route("admin.blog.refresh")); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error refreshing posts. Please try again.');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\blog\index.blade.php ENDPATH**/ ?>