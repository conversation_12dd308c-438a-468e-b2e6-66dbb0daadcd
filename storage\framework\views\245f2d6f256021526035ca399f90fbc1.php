<?php $__env->startSection('title', 'Navigation Management'); ?>

<?php $__env->startSection('content'); ?>
<style>
    .sortable {
        list-style: none;
        padding: 0;
    }

    .sortable li {
        background: white;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;
        cursor: move;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;
    }

    .sortable li:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .sortable li.ui-sortable-helper {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        transform: rotate(2deg);
    }

    .navbar-item-info {
        display: flex;
        align-items: center;
        gap: 15px;
        flex: 1;
    }

    .drag-handle {
        color: #999;
        font-size: 18px;
        cursor: move;
    }

    .navbar-item-details h4 {
        margin: 0 0 5px 0;
        font-size: 16px;
        font-weight: 600;
    }

    .navbar-item-details p {
        margin: 0;
        font-size: 14px;
        color: #666;
    }

    .navbar-item-actions {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-active {
        background-color: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }

    .color-preview {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid #ddd;
        display: inline-block;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 20px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #28a745;
    }

    input:checked + .slider:before {
        transform: translateX(20px);
    }
</style>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Navigation Items</h3>
            <a href="<?php echo e(route('admin.navbar.create')); ?>" class="btn btn-success">Add New Item</a>
        </div>
    </div>
    <div class="card-body">
        <?php if($navbarItems->count() > 0): ?>
            <p style="color: #666; margin-bottom: 20px;">
                <strong>Tip:</strong> Drag and drop items to reorder them. Use the toggle switch to activate/deactivate items.
            </p>
            
            <ul id="sortable-navbar" class="sortable">
                <?php $__currentLoopData = $navbarItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li data-id="<?php echo e($item->id); ?>">
                    <div class="navbar-item-info">
                        <span class="drag-handle">⋮⋮</span>
                        
                        <div class="navbar-item-details">
                            <h4>
                                <?php echo e($item->title); ?>

                                <?php if($item->color): ?>
                                    <span class="color-preview" style="background-color: <?php echo e($item->color); ?>"></span>
                                <?php endif; ?>
                                <?php if($item->auto_generated): ?>
                                    <span style="font-size: 12px; color: #666;">(Auto-generated)</span>
                                <?php endif; ?>
                            </h4>
                            <p><?php echo e($item->url); ?> • Order: <?php echo e($item->order); ?></p>
                        </div>
                    </div>
                    
                    <div class="navbar-item-actions">
                        <label class="toggle-switch">
                            <input type="checkbox" <?php echo e($item->is_active ? 'checked' : ''); ?> 
                                   onchange="toggleItem(<?php echo e($item->id); ?>, this)">
                            <span class="slider"></span>
                        </label>
                        
                        <a href="<?php echo e(route('admin.navbar.edit', $item)); ?>" class="btn btn-sm">Edit</a>
                        
                        <form method="POST" action="<?php echo e(route('admin.navbar.destroy', $item)); ?>" 
                              style="display: inline;" onsubmit="return confirm('Are you sure?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                        </form>
                    </div>
                </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        <?php else: ?>
            <div style="text-align: center; padding: 40px 20px;">
                <p style="color: #666; font-size: 16px;">No navigation items found.</p>
                <a href="<?php echo e(route('admin.navbar.create')); ?>" class="btn">Create First Item</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    $("#sortable-navbar").sortable({
        handle: '.drag-handle',
        update: function(event, ui) {
            var order = $(this).sortable('toArray', {attribute: 'data-id'});
            
            $.ajax({
                url: '<?php echo e(route("admin.navbar.order")); ?>',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    items: order
                },
                success: function(response) {
                    if (response.success) {
                        // Update order numbers in the UI
                        $('#sortable-navbar li').each(function(index) {
                            $(this).find('.navbar-item-details p').text(function(i, text) {
                                return text.replace(/Order: \d+/, 'Order: ' + (index + 1));
                            });
                        });
                    }
                },
                error: function() {
                    alert('Error updating order. Please refresh the page.');
                }
            });
        }
    });
});

function toggleItem(id, checkbox) {
    $.ajax({
        url: '/admin/navbar/' + id + '/toggle',
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                // Visual feedback could be added here
                console.log('Item toggled successfully');
            }
        },
        error: function() {
            // Revert checkbox state on error
            checkbox.checked = !checkbox.checked;
            alert('Error updating item status. Please try again.');
        }
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\navbar\index.blade.php ENDPATH**/ ?>