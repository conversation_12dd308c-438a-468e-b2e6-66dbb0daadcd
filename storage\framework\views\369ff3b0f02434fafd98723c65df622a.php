<?php $__env->startSection('title', '<PERSON> - <PERSON>'); ?>
<?php $__env->startSection('description', 'Get in touch with <PERSON> for web development, design projects, and collaborations.'); ?>

<?php $__env->startSection('content'); ?>
<div class="section" style="padding-top: 6rem;">
    <div class="container">
        <!-- <PERSON> Header -->
        <div class="text-center mb-2xl">
            <h1 class="text-5xl font-bold mb-lg">Get In Touch</h1>
            <p class="text-xl text-muted max-w-2xl mx-auto">
                Have a project in mind? I'd love to hear about it. 
                Let's discuss how we can work together to bring your ideas to life.
            </p>
        </div>
        
        <div class="grid lg:grid-cols-2 gap-3xl">
            <!-- Contact Form -->
            <div class="card">
                <h2 class="text-2xl font-bold mb-xl">Send Me a Message</h2>
                
                <?php if(session('success')): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-lg py-md rounded mb-lg">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>
                
                <form action="<?php echo e(route('contact.submit')); ?>" method="POST" class="space-y-lg">
                    <?php echo csrf_field(); ?>
                    
                    <div class="grid md:grid-cols-2 gap-lg">
                        <div>
                            <label for="name" class="block text-sm font-medium mb-sm">Name *</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="<?php echo e(old('name')); ?>"
                                   required
                                   class="w-full px-lg py-md border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium mb-sm">Email *</label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo e(old('email')); ?>"
                                   required
                                   class="w-full px-lg py-md border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium mb-sm">Subject *</label>
                        <input type="text" 
                               id="subject" 
                               name="subject" 
                               value="<?php echo e(old('subject')); ?>"
                               required
                               class="w-full px-lg py-md border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium mb-sm">Message *</label>
                        <textarea id="message" 
                                  name="message" 
                                  rows="6" 
                                  required
                                  class="w-full px-lg py-md border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"><?php echo e(old('message')); ?></textarea>
                        <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-full">
                        Send Message
                    </button>
                </form>
            </div>
            
            <!-- Contact Information -->
            <div class="space-y-xl">
                <div class="card">
                    <h3 class="text-xl font-bold mb-lg">Contact Information</h3>
                    
                    <div class="space-y-lg">
                        <div class="flex items-start gap-md">
                            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white">
                                📧
                            </div>
                            <div>
                                <h4 class="font-semibold">Email</h4>
                                <p class="text-muted"><?php echo e($settings['contact_email'] ?? '<EMAIL>'); ?></p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-md">
                            <div class="w-10 h-10 bg-secondary rounded-full flex items-center justify-center text-white">
                                📱
                            </div>
                            <div>
                                <h4 class="font-semibold">Phone</h4>
                                <p class="text-muted"><?php echo e($settings['contact_phone'] ?? '+****************'); ?></p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-md">
                            <div class="w-10 h-10 bg-accent rounded-full flex items-center justify-center text-white">
                                📍
                            </div>
                            <div>
                                <h4 class="font-semibold">Location</h4>
                                <p class="text-muted"><?php echo e($settings['contact_location'] ?? 'Available Worldwide'); ?></p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-md">
                            <div class="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-white">
                                ⏰
                            </div>
                            <div>
                                <h4 class="font-semibold">Response Time</h4>
                                <p class="text-muted">Usually within 24 hours</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3 class="text-xl font-bold mb-lg">Follow Me</h3>
                    
                    <div class="grid grid-cols-2 gap-md">
                        <a href="<?php echo e($settings['social_linkedin'] ?? '#'); ?>" 
                           class="flex items-center gap-sm p-md border rounded-lg hover:bg-gray-50 transition">
                            <span class="text-blue-600">💼</span>
                            <span class="text-sm">LinkedIn</span>
                        </a>
                        
                        <a href="<?php echo e($settings['social_github'] ?? '#'); ?>" 
                           class="flex items-center gap-sm p-md border rounded-lg hover:bg-gray-50 transition">
                            <span class="text-gray-800">🐙</span>
                            <span class="text-sm">GitHub</span>
                        </a>
                        
                        <a href="<?php echo e($settings['social_twitter'] ?? '#'); ?>" 
                           class="flex items-center gap-sm p-md border rounded-lg hover:bg-gray-50 transition">
                            <span class="text-blue-400">🐦</span>
                            <span class="text-sm">Twitter</span>
                        </a>
                        
                        <a href="<?php echo e($settings['social_instagram'] ?? '#'); ?>" 
                           class="flex items-center gap-sm p-md border rounded-lg hover:bg-gray-50 transition">
                            <span class="text-pink-500">📷</span>
                            <span class="text-sm">Instagram</span>
                        </a>
                    </div>
                </div>
                
                <div class="card">
                    <h3 class="text-xl font-bold mb-lg">Project Types</h3>
                    
                    <div class="space-y-sm">
                        <div class="flex items-center gap-sm">
                            <span class="text-primary">✓</span>
                            <span class="text-sm">Web Development</span>
                        </div>
                        <div class="flex items-center gap-sm">
                            <span class="text-primary">✓</span>
                            <span class="text-sm">UI/UX Design</span>
                        </div>
                        <div class="flex items-center gap-sm">
                            <span class="text-primary">✓</span>
                            <span class="text-sm">E-commerce Solutions</span>
                        </div>
                        <div class="flex items-center gap-sm">
                            <span class="text-primary">✓</span>
                            <span class="text-sm">Custom Applications</span>
                        </div>
                        <div class="flex items-center gap-sm">
                            <span class="text-primary">✓</span>
                            <span class="text-sm">3D Design & Modeling</span>
                        </div>
                        <div class="flex items-center gap-sm">
                            <span class="text-primary">✓</span>
                            <span class="text-sm">Graphic Design</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.space-y-lg > * + * {
    margin-top: 1.5rem;
}

.space-y-xl > * + * {
    margin-top: 2rem;
}

.space-y-sm > * + * {
    margin-top: 0.5rem;
}

.w-10 {
    width: 2.5rem;
}

.h-10 {
    height: 2.5rem;
}

.w-full {
    width: 100%;
}

.max-w-2xl {
    max-width: 42rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 768px) {
    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
    }
}

.bg-green-100 {
    background-color: #dcfce7;
}

.border-green-400 {
    border-color: #4ade80;
}

.text-green-700 {
    color: #15803d;
}

.text-red-500 {
    color: #ef4444;
}

.text-xs {
    font-size: 0.75rem;
}

.border-gray-300 {
    border-color: #d1d5db;
}

.focus\:ring-2:focus {
    box-shadow: 0 0 0 2px var(--primary-color);
}

.focus\:ring-primary:focus {
    --tw-ring-color: var(--primary-color);
}

.focus\:border-transparent:focus {
    border-color: transparent;
}

.hover\:bg-gray-50:hover {
    background-color: #f9fafb;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\pages\contact.blade.php ENDPATH**/ ?>