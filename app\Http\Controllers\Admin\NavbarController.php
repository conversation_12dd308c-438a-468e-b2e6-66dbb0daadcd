<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NavbarItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NavbarController extends Controller
{
    public function index()
    {
        $navbarItems = NavbarItem::orderBy('order')->get();
        return view('admin.navbar.index', compact('navbarItems'));
    }

    public function create()
    {
        return view('admin.navbar.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'url' => 'required|string|max:255',
            'order' => 'required|integer|min:1',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'auto_generated' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        NavbarItem::create([
            'title' => $request->title,
            'slug' => \Str::slug($request->title),
            'url' => $request->url,
            'order' => $request->order,
            'color' => $request->color,
            'auto_generated' => $request->boolean('auto_generated'),
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('admin.navbar.index')->with('success', 'Navbar item created successfully!');
    }

    public function edit(NavbarItem $navbar)
    {
        return view('admin.navbar.edit', compact('navbar'));
    }

    public function update(Request $request, NavbarItem $navbar)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'url' => 'required|string|max:255',
            'order' => 'required|integer|min:1',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'auto_generated' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $navbar->update([
            'title' => $request->title,
            'slug' => \Str::slug($request->title),
            'url' => $request->url,
            'order' => $request->order,
            'color' => $request->color,
            'auto_generated' => $request->boolean('auto_generated'),
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('admin.navbar.index')->with('success', 'Navbar item updated successfully!');
    }

    public function destroy(NavbarItem $navbar)
    {
        $navbar->delete();
        return redirect()->route('admin.navbar.index')->with('success', 'Navbar item deleted successfully!');
    }

    public function updateOrder(Request $request)
    {
        $items = $request->input('items', []);
        
        foreach ($items as $index => $id) {
            NavbarItem::where('id', $id)->update(['order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }

    public function toggle(NavbarItem $navbar)
    {
        $navbar->update(['is_active' => !$navbar->is_active]);
        
        return response()->json([
            'success' => true,
            'is_active' => $navbar->is_active
        ]);
    }
}
