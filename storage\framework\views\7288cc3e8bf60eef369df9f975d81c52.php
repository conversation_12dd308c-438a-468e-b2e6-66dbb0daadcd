<?php $__env->startSection('title', 'Add New Project Stat'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3>Add New Project Stat</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="<?php echo e(route('admin.project-stats.store')); ?>">
            <?php echo csrf_field(); ?>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="title">Stat Title *</label>
                        <input type="text" id="title" name="title" class="form-control" 
                               value="<?php echo e(old('title')); ?>" required>
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="count">Count *</label>
                        <input type="number" id="count" name="count" class="form-control" 
                               value="<?php echo e(old('count', 0)); ?>" min="0" required>
                        <?php $__errorArgs = ['count'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="icon">Icon (Emoji)</label>
                        <input type="text" id="icon" name="icon" class="form-control" 
                               value="<?php echo e(old('icon')); ?>" placeholder="📊" maxlength="10">
                        <small style="color: #666;">Enter an emoji or short text to represent this stat</small>
                        <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="order">Display Order *</label>
                        <input type="number" id="order" name="order" class="form-control" 
                               value="<?php echo e(old('order', 1)); ?>" min="1" required>
                        <small style="color: #666;">Lower numbers appear first</small>
                        <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div style="color: #dc3545; font-size: 14px; margin-top: 5px;"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" name="is_active" value="1" <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                            <span>Active</span>
                        </label>
                        <small style="color: #666;">Uncheck to hide this stat from the website</small>
                    </div>
                </div>
                
                <!-- Right Column - Preview -->
                <div>
                    <h5 style="margin-bottom: 20px; color: #333;">Preview</h5>
                    
                    <div style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 25px; text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 15px; color: #667eea;" id="preview-icon">📊</div>
                        <div style="font-size: 36px; font-weight: 700; color: #333; margin-bottom: 10px;" id="preview-count">0</div>
                        <div style="font-size: 16px; font-weight: 600; color: #666;" id="preview-title">Stat Title</div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <h6 style="color: #333; margin-bottom: 10px;">Common Icons:</h6>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                            <button type="button" onclick="setIcon('🎨')" style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">🎨</button>
                            <button type="button" onclick="setIcon('💻')" style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">💻</button>
                            <button type="button" onclick="setIcon('📱')" style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">📱</button>
                            <button type="button" onclick="setIcon('🚀')" style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">🚀</button>
                            <button type="button" onclick="setIcon('⚡')" style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">⚡</button>
                            <button type="button" onclick="setIcon('🎯')" style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">🎯</button>
                            <button type="button" onclick="setIcon('📊')" style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">📊</button>
                            <button type="button" onclick="setIcon('✨')" style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">✨</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 30px;">
                <button type="submit" class="btn">Create Project Stat</button>
                <a href="<?php echo e(route('admin.project-stats.index')); ?>" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>

<script>
// Update preview when form fields change
document.getElementById('title').addEventListener('input', function() {
    document.getElementById('preview-title').textContent = this.value || 'Stat Title';
});

document.getElementById('count').addEventListener('input', function() {
    document.getElementById('preview-count').textContent = this.value || '0';
});

document.getElementById('icon').addEventListener('input', function() {
    document.getElementById('preview-icon').textContent = this.value || '📊';
});

function setIcon(icon) {
    document.getElementById('icon').value = icon;
    document.getElementById('preview-icon').textContent = icon;
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\admin\project-stats\create.blade.php ENDPATH**/ ?>