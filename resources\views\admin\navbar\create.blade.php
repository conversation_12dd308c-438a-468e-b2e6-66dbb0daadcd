@extends('layouts.admin')

@section('title', 'Add Navigation Item')

@section('content')
<div class="card">
    <div class="card-header">
        <h3>Add New Navigation Item</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('admin.navbar.store') }}">
            @csrf
            
            <div class="form-group">
                <label for="title">Title *</label>
                <input type="text" id="title" name="title" class="form-control" 
                       value="{{ old('title') }}" required>
                @error('title')
                    <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="url">URL *</label>
                <input type="text" id="url" name="url" class="form-control" 
                       value="{{ old('url') }}" placeholder="/about" required>
                <small style="color: #666;">Enter the URL path (e.g., /about, /contact, or external URL)</small>
                @error('url')
                    <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="order">Order *</label>
                <input type="number" id="order" name="order" class="form-control" 
                       value="{{ old('order', 1) }}" min="1" required>
                <small style="color: #666;">Lower numbers appear first in the navigation</small>
                @error('order')
                    <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="color">Custom Color (Optional)</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <input type="color" id="color" name="color" value="{{ old('color', '#667eea') }}">
                    <input type="text" class="form-control" style="flex: 1;" 
                           value="{{ old('color', '#667eea') }}" readonly id="color-text">
                </div>
                <small style="color: #666;">Choose a custom color for this navigation item</small>
                @error('color')
                    <div style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label style="display: flex; align-items: center; gap: 10px;">
                    <input type="checkbox" name="auto_generated" value="1" {{ old('auto_generated') ? 'checked' : '' }}>
                    <span>Auto-generated Page</span>
                </label>
                <small style="color: #666;">Check this if this item should generate a standalone page automatically</small>
            </div>
            
            <div class="form-group">
                <label style="display: flex; align-items: center; gap: 10px;">
                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                    <span>Active</span>
                </label>
                <small style="color: #666;">Uncheck to hide this item from the navigation</small>
            </div>
            
            <div style="display: flex; gap: 10px;">
                <button type="submit" class="btn">Create Navigation Item</button>
                <a href="{{ route('admin.navbar.index') }}" class="btn" style="background: #6c757d;">Cancel</a>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('color').addEventListener('change', function() {
    document.getElementById('color-text').value = this.value;
});
</script>
@endsection
