<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', '<PERSON>lio'); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('description', 'Professional portfolio of <PERSON> - <PERSON> Develo<PERSON>, Designer, and Creative Professional'); ?>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link href="<?php echo e(asset('css/dynamic-styles.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/responsive.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/animations.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(route('dynamic.styles')); ?>" rel="stylesheet">
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Skip Link -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <a href="<?php echo e(url('/')); ?>" class="logo">
                <?php echo e($settings['site_name'] ?? 'Aziz Khan'); ?>

            </a>
            
            <ul class="nav-links">
                <?php $__currentLoopData = $navbarItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <a href="<?php echo e($item->url); ?>" 
                           class="<?php echo e(request()->is(trim($item->url, '/')) ? 'active' : ''); ?>">
                            <?php echo e($item->title); ?>

                        </a>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            
            <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                ☰
            </button>
        </div>
        
        <div class="mobile-menu">
            <?php $__currentLoopData = $navbarItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e($item->url); ?>"><?php echo e($item->title); ?></a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main id="main-content">
        <?php echo $__env->yieldContent('content'); ?>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><?php echo e($settings['site_name'] ?? 'Aziz Khan'); ?></h3>
                    <p><?php echo e($settings['footer_description'] ?? 'Professional web developer and designer creating amazing digital experiences.'); ?></p>
                </div>
                
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <?php $__currentLoopData = $navbarItems->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <p><a href="<?php echo e($item->url); ?>"><?php echo e($item->title); ?></a></p>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <div class="footer-section">
                    <h3>Services</h3>
                    <p><a href="<?php echo e(url('/services')); ?>">Web Development</a></p>
                    <p><a href="<?php echo e(url('/services')); ?>">UI/UX Design</a></p>
                    <p><a href="<?php echo e(url('/services')); ?>">3D Design</a></p>
                    <p><a href="<?php echo e(url('/services')); ?>">Graphic Design</a></p>
                </div>
                
                <div class="footer-section">
                    <h3>Connect</h3>
                    <p><a href="mailto:<?php echo e($settings['contact_email'] ?? '<EMAIL>'); ?>">Email</a></p>
                    <p><a href="<?php echo e($settings['social_linkedin'] ?? '#'); ?>">LinkedIn</a></p>
                    <p><a href="<?php echo e($settings['social_github'] ?? '#'); ?>">GitHub</a></p>
                    <p><a href="<?php echo e($settings['social_twitter'] ?? '#'); ?>">Twitter</a></p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo e(date('Y')); ?> <?php echo e($settings['site_name'] ?? 'Aziz Khan'); ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="<?php echo e(asset('js/animations.js')); ?>"></script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\aziz-new-portfolio\resources\views\layouts\page.blade.php ENDPATH**/ ?>