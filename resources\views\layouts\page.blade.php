<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', '<PERSON>')</title>
    <meta name="description" content="@yield('description', 'Professional portfolio of <PERSON> - <PERSON> Developer, Designer, and Creative Professional')">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link href="{{ asset('css/dynamic-styles.css') }}" rel="stylesheet">
    <link href="{{ asset('css/responsive.css') }}" rel="stylesheet">
    <link href="{{ asset('css/animations.css') }}" rel="stylesheet">
    <link href="{{ route('dynamic.styles') }}" rel="stylesheet">
    
    @stack('styles')
</head>
<body>
    <!-- Skip Link -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <a href="{{ url('/') }}" class="logo">
                {{ $settings['site_name'] ?? 'Aziz Khan' }}
            </a>
            
            <ul class="nav-links">
                @foreach($navbarItems as $item)
                    <li>
                        <a href="{{ $item->url }}" 
                           class="{{ request()->is(trim($item->url, '/')) ? 'active' : '' }}">
                            {{ $item->title }}
                        </a>
                    </li>
                @endforeach
            </ul>
            
            <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                ☰
            </button>
        </div>
        
        <div class="mobile-menu">
            @foreach($navbarItems as $item)
                <a href="{{ $item->url }}">{{ $item->title }}</a>
            @endforeach
        </div>
    </nav>
    
    <!-- Main Content -->
    <main id="main-content">
        @yield('content')
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>{{ $settings['site_name'] ?? 'Aziz Khan' }}</h3>
                    <p>{{ $settings['footer_description'] ?? 'Professional web developer and designer creating amazing digital experiences.' }}</p>
                </div>
                
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    @foreach($navbarItems->take(4) as $item)
                        <p><a href="{{ $item->url }}">{{ $item->title }}</a></p>
                    @endforeach
                </div>
                
                <div class="footer-section">
                    <h3>Services</h3>
                    <p><a href="{{ url('/services') }}">Web Development</a></p>
                    <p><a href="{{ url('/services') }}">UI/UX Design</a></p>
                    <p><a href="{{ url('/services') }}">3D Design</a></p>
                    <p><a href="{{ url('/services') }}">Graphic Design</a></p>
                </div>
                
                <div class="footer-section">
                    <h3>Connect</h3>
                    <p><a href="mailto:{{ $settings['contact_email'] ?? '<EMAIL>' }}">Email</a></p>
                    <p><a href="{{ $settings['social_linkedin'] ?? '#' }}">LinkedIn</a></p>
                    <p><a href="{{ $settings['social_github'] ?? '#' }}">GitHub</a></p>
                    <p><a href="{{ $settings['social_twitter'] ?? '#' }}">Twitter</a></p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; {{ date('Y') }} {{ $settings['site_name'] ?? 'Aziz Khan' }}. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="{{ asset('js/animations.js') }}"></script>
    @stack('scripts')
</body>
</html>
