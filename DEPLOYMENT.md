# <PERSON> - Deployment Guide

## Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Set `APP_ENV=production` in `.env`
- [ ] Set `APP_DEBUG=false` in `.env`
- [ ] Configure database credentials
- [ ] Set up mail configuration
- [ ] Configure file storage (if using cloud storage)

### 2. Security Configuration
- [ ] Generate new `APP_KEY` using `php artisan key:generate`
- [ ] Set strong database passwords
- [ ] Configure HTTPS/SSL certificates
- [ ] Set up proper file permissions (755 for directories, 644 for files)
- [ ] Secure `.env` file (600 permissions)

### 3. Performance Optimization
```bash
# Run the optimization command
php artisan portfolio:optimize

# Or run individual commands:
php artisan config:cache
php artisan route:cache
php artisan view:cache
composer dump-autoload --optimize
```

### 4. Database Setup
```bash
# Run migrations
php artisan migrate --force

# Seed default data
php artisan db:seed --class=DatabaseSeeder
```

### 5. File Permissions
```bash
# Set proper permissions
chmod -R 755 storage bootstrap/cache
chmod -R 755 public/images
chown -R www-data:www-data storage bootstrap/cache
```

### 6. Web Server Configuration

#### Apache (.htaccess)
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/portfolio/public;
    
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    
    index index.php;
    
    charset utf-8;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }
    
    error_page 404 /index.php;
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Testing

### Run Test Suite
```bash
# Run all tests
php artisan portfolio:test

# Run with coverage
php artisan portfolio:test --coverage

# Run specific test suites
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit
```

### Manual Testing Checklist
- [ ] Homepage loads correctly
- [ ] Admin login works
- [ ] All CRUD operations function
- [ ] File uploads work
- [ ] Contact form submits
- [ ] Responsive design works on mobile
- [ ] All animations function
- [ ] Dynamic CSS generation works
- [ ] Rate limiting is active
- [ ] Security headers are present

## Monitoring & Maintenance

### Log Monitoring
- Monitor `storage/logs/laravel.log` for errors
- Set up log rotation
- Configure error reporting/alerting

### Performance Monitoring
- Monitor database query performance
- Check cache hit rates
- Monitor memory usage
- Track page load times

### Regular Maintenance
```bash
# Clear old logs (weekly)
php artisan log:clear

# Optimize application (after updates)
php artisan portfolio:optimize

# Update dependencies (monthly)
composer update
npm update
```

### Backup Strategy
- Database backups (daily)
- File storage backups (weekly)
- Code repository backups (automatic with Git)

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check file permissions
   - Verify `.env` configuration
   - Check error logs

2. **Database Connection Issues**
   - Verify database credentials
   - Check database server status
   - Ensure database exists

3. **File Upload Issues**
   - Check storage permissions
   - Verify upload limits in PHP
   - Check disk space

4. **Performance Issues**
   - Run optimization commands
   - Check database indexes
   - Monitor server resources

### Debug Mode (Development Only)
```bash
# Enable debug mode temporarily
php artisan down
# Set APP_DEBUG=true in .env
php artisan config:clear
php artisan up
```

## Security Best Practices

1. **Regular Updates**
   - Keep Laravel updated
   - Update dependencies regularly
   - Monitor security advisories

2. **Access Control**
   - Use strong passwords
   - Implement 2FA if needed
   - Regular user access reviews

3. **Server Security**
   - Keep server OS updated
   - Configure firewall
   - Use HTTPS only
   - Regular security scans

## Support

For technical support or questions:
- Check the Laravel documentation
- Review application logs
- Contact the development team

---

**Last Updated:** {{ date('Y-m-d') }}
**Version:** 1.0.0
